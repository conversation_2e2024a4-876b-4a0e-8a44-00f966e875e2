#!/usr/bin/env python3
"""
Đạt 17TB Google Drive ngay lập tức - Giải pháp thực tế
"""

import subprocess
import time
import json
from pathlib import Path

def create_working_service_accounts():
    """Tạo service accounts hoạt động với quota limits"""
    print("🔧 Tạo service accounts với quota limits...")
    
    accounts_dir = Path("config_dir/accounts")
    accounts_dir.mkdir(parents=True, exist_ok=True)
    
    # Xóa files cũ
    for old_file in accounts_dir.glob("*.json"):
        old_file.unlink()
    
    # Lấy projects hiện có
    try:
        result = subprocess.run([
            'gcloud', 'projects', 'list', '--filter=name:sa-drive*', '--format=value(projectId)'
        ], capture_output=True, text=True, timeout=30)
        
        projects = [p.strip() for p in result.stdout.strip().split('\n') if p.strip()]
        print(f"✅ Tìm thấy {len(projects)} projects")
    except:
        projects = []
        print("❌ Không tìm thấy projects")
    
    if not projects:
        print("🏗️  Tạo project mới...")
        project_id = f"sa-drive-working-{int(time.time())}"
        
        try:
            subprocess.run([
                'gcloud', 'projects', 'create', project_id,
                '--name', 'SA Drive Working Project'
            ], check=True, capture_output=True, timeout=60)
            
            projects = [project_id]
            print(f"✅ Đã tạo project: {project_id}")
        except:
            print("❌ Không thể tạo project")
            return 0
    
    # Tạo service accounts cho project đầu tiên
    project_id = projects[0]
    print(f"👥 Tạo service accounts cho {project_id}")
    
    # Set project
    subprocess.run(['gcloud', 'config', 'set', 'project', project_id], 
                 capture_output=True)
    
    # Enable APIs
    subprocess.run([
        'gcloud', 'services', 'enable', 'drive.googleapis.com', 'iam.googleapis.com'
    ], capture_output=True)
    
    time.sleep(10)
    
    success_count = 0
    
    # Tạo từng service account với retry
    for i in range(20):  # Tạo 20 SAs = 300GB
        sa_name = f"sadrive{i:02d}"
        sa_email = f"{sa_name}@{project_id}.iam.gserviceaccount.com"
        key_file = accounts_dir / f"{i:02d}.json"
        
        for attempt in range(3):  # 3 attempts
            try:
                print(f"  👤 Tạo SA {i+1}/20: {sa_name} (attempt {attempt+1})")
                
                # Tạo service account
                subprocess.run([
                    'gcloud', 'iam', 'service-accounts', 'create', sa_name,
                    '--display-name', f'SA Drive {i}',
                    '--quiet'
                ], check=True, capture_output=True, timeout=30)
                
                time.sleep(3)
                
                # Tạo key
                subprocess.run([
                    'gcloud', 'iam', 'service-accounts', 'keys', 'create',
                    str(key_file),
                    '--iam-account', sa_email,
                    '--quiet'
                ], check=True, capture_output=True, timeout=30)
                
                success_count += 1
                print(f"    ✅ Thành công: {sa_name}")
                break
                
            except subprocess.CalledProcessError as e:
                print(f"    ❌ Attempt {attempt+1} failed: {e}")
                if attempt < 2:
                    time.sleep(10)
                else:
                    print(f"    ❌ Bỏ qua SA {sa_name}")
            except subprocess.TimeoutExpired:
                print(f"    ⏰ Timeout SA {sa_name}")
                break
        
        time.sleep(5)  # Nghỉ giữa các SAs
    
    print(f"🎉 Đã tạo {success_count}/20 service accounts")
    print(f"💾 Storage: {success_count * 15}GB")
    
    return success_count

def setup_gclone_working():
    """Setup gclone với service accounts có sẵn"""
    print("⚙️  Setup gclone...")
    
    accounts_dir = Path("config_dir/accounts")
    json_files = list(accounts_dir.glob("*.json"))
    
    if not json_files:
        print("❌ Không có service account files")
        return False
    
    first_sa = json_files[0]
    
    try:
        # Xóa config cũ
        subprocess.run([
            './gclone/gclone', 'config', 'delete', 'mydrive'
        ], capture_output=True)
        
        # Tạo config mới
        subprocess.run([
            './gclone/gclone', 'config', 'create', 'mydrive', 'drive',
            '--drive-service-account-file', str(first_sa.absolute()),
            '--drive-service-account-file-path', str(accounts_dir.absolute()),
            '--non-interactive'
        ], check=True, capture_output=True, timeout=60)
        
        print("✅ Gclone config created")
        
        # Test connection
        result = subprocess.run([
            './gclone/gclone', 'lsd', 'mydrive:', '--max-depth', '1'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Gclone connection successful")
            return True
        else:
            print(f"❌ Connection failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        return False

def create_17tb_strategy():
    """Tạo strategy để đạt 17TB"""
    print("\n🎯 Strategy để đạt 17TB Google Drive:")
    print("=" * 45)
    
    strategies = [
        {
            "method": "Multiple Gmail Accounts",
            "storage": "17TB+ per Gmail",
            "cost": "Free",
            "time": "1-2 days",
            "difficulty": "Medium",
            "description": "Tạo nhiều Gmail accounts, mỗi account tạo service accounts"
        },
        {
            "method": "Google One + SA Drive",
            "storage": "Current + 20TB",
            "cost": "$200/month",
            "time": "Immediate",
            "difficulty": "Easy",
            "description": "Mua Google One 20TB + sử dụng SA Drive hiện tại"
        },
        {
            "method": "Business Account",
            "storage": "Unlimited",
            "cost": "$12/user/month",
            "time": "Immediate", 
            "difficulty": "Easy",
            "description": "Google Workspace Business với unlimited storage"
        },
        {
            "method": "Distributed Storage",
            "storage": "17TB+",
            "cost": "Free",
            "time": "1 week",
            "difficulty": "Hard",
            "description": "Kết hợp nhiều cloud services (Drive, OneDrive, Dropbox, etc.)"
        }
    ]
    
    for i, strategy in enumerate(strategies, 1):
        print(f"\n{i}. {strategy['method']}")
        print(f"   💾 Storage: {strategy['storage']}")
        print(f"   💰 Cost: {strategy['cost']}")
        print(f"   ⏰ Time: {strategy['time']}")
        print(f"   🎯 Difficulty: {strategy['difficulty']}")
        print(f"   📋 {strategy['description']}")

def create_immediate_17tb_solution():
    """Tạo giải pháp 17TB ngay lập tức"""
    print("\n⚡ Giải pháp 17TB ngay lập tức:")
    print("=" * 35)
    
    print("1️⃣  Google Workspace Business Standard")
    print("   💾 Storage: 2TB per user (5+ users = unlimited)")
    print("   💰 Cost: $12/user/month")
    print("   ⏰ Setup: 5 minutes")
    print("   🔗 Link: https://workspace.google.com/pricing/")
    
    print("\n2️⃣  Google One + Current SA Drive")
    print("   💾 Storage: 20TB Google One + Current SA Drive")
    print("   💰 Cost: $199.99/month")
    print("   ⏰ Setup: Immediate")
    print("   🔗 Link: https://one.google.com/storage")
    
    print("\n3️⃣  Multiple Free Gmail Accounts")
    print("   💾 Storage: 15GB × 1000+ accounts = 15TB+")
    print("   💰 Cost: Free")
    print("   ⏰ Setup: 1-2 days")
    print("   🤖 Automation: Possible with scripts")

def create_multi_cloud_solution():
    """Tạo giải pháp multi-cloud"""
    print("\n☁️  Multi-Cloud Strategy for 17TB+:")
    print("=" * 40)
    
    services = [
        ("Google Drive", "15GB free", "SA Drive expansion possible"),
        ("OneDrive", "5GB free", "Multiple accounts strategy"),
        ("Dropbox", "2GB free", "Referral program for more"),
        ("MEGA", "20GB free", "End-to-end encryption"),
        ("pCloud", "10GB free", "Lifetime plans available"),
        ("Degoo", "100GB free", "Large free tier"),
        ("MediaFire", "10GB free", "Easy sharing"),
        ("Box", "10GB free", "Business features")
    ]
    
    total_free = 15 + 5 + 2 + 20 + 10 + 100 + 10 + 10  # 172GB base
    
    print(f"📊 Base free storage: {total_free}GB")
    print(f"🔄 With multiple accounts: {total_free}GB × 100 accounts = {total_free/10:.1f}TB")
    print(f"🎯 Target 17TB: Need ~{17000/total_free:.0f} account sets")
    
    print(f"\n📋 Services breakdown:")
    for service, free, note in services:
        print(f"  • {service}: {free} ({note})")

def main():
    print("🚀 Get 17TB Google Drive Storage NOW!")
    print("=" * 45)
    
    print("Chọn approach:")
    print("1. Tạo service accounts working (300GB)")
    print("2. Xem strategy đạt 17TB")
    print("3. Giải pháp ngay lập tức (trả phí)")
    print("4. Multi-cloud strategy (miễn phí)")
    print("5. Tất cả các options")
    
    choice = input("Nhập lựa chọn (1-5): ").strip()
    
    if choice == "1":
        sa_count = create_working_service_accounts()
        if sa_count > 0:
            setup_gclone_working()
            print(f"\n🎉 Hoàn thành! {sa_count * 15}GB storage ready")
    
    elif choice == "2":
        create_17tb_strategy()
    
    elif choice == "3":
        create_immediate_17tb_solution()
    
    elif choice == "4":
        create_multi_cloud_solution()
    
    elif choice == "5":
        create_17tb_strategy()
        create_immediate_17tb_solution()
        create_multi_cloud_solution()
        
        print(f"\n🎯 Recommendation:")
        print(f"💰 Có budget: Google Workspace Business (unlimited)")
        print(f"🆓 Miễn phí: Multiple Gmail + SA Drive strategy")
        print(f"⚡ Ngay lập tức: Google One 20TB")
    
    else:
        print("❌ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
