#!/usr/bin/env python3
"""
Monitor 17TiB infrastructure creation progress
"""

import time
import json
from pathlib import Path

def monitor_progress():
    """Monitor tiến trình tạo infrastructure"""
    print("📊 17TiB Infrastructure Progress Monitor")
    print("=" * 45)
    
    accounts_dir = Path("config_dir/accounts")
    target_sas = 1200  # 12 projects × 100 SAs
    
    while True:
        # Đếm service accounts hiện có
        json_files = list(accounts_dir.glob("*.json"))
        current_sas = len(json_files)
        
        # Tính toán progress
        progress_percent = (current_sas / target_sas) * 100
        storage_gb = current_sas * 15
        storage_tb = storage_gb / 1024
        
        # Hiển thị progress
        print(f"\r📊 Progress: {current_sas:4d}/{target_sas} SAs ({progress_percent:5.1f}%) | "
              f"Storage: {storage_gb:5.0f}GB ({storage_tb:4.1f}TiB)", end="", flush=True)
        
        # Kiểm tra hoàn thành
        if current_sas >= target_sas:
            print(f"\n🎉 Infrastructure hoàn thành!")
            print(f"📊 Total: {current_sas} service accounts")
            print(f"💾 Storage: {storage_gb}GB = {storage_tb:.1f}TiB")
            break
        
        # Đợi 10 giây
        time.sleep(10)

def check_projects():
    """Kiểm tra projects đã tạo"""
    print("\n🏗️  Checking Google Cloud Projects...")
    
    try:
        import subprocess
        result = subprocess.run([
            'gcloud', 'projects', 'list', '--filter=name:sa-drive*', '--format=value(projectId)'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            projects = [p.strip() for p in result.stdout.strip().split('\n') if p.strip()]
            print(f"📋 Found {len(projects)} SA Drive projects:")
            for i, project in enumerate(projects, 1):
                print(f"  {i:2d}. {project}")
            return projects
        else:
            print("❌ Không thể list projects")
            return []
    
    except Exception as e:
        print(f"❌ Lỗi check projects: {e}")
        return []

def estimate_completion():
    """Ước tính thời gian hoàn thành"""
    accounts_dir = Path("config_dir/accounts")
    json_files = list(accounts_dir.glob("*.json"))
    current_sas = len(json_files)
    
    if current_sas < 10:
        print("⏳ Chưa đủ dữ liệu để ước tính")
        return
    
    # Giả sử tốc độ tạo SA: 1 SA/3 giây
    remaining_sas = 1200 - current_sas
    estimated_seconds = remaining_sas * 3
    estimated_hours = estimated_seconds / 3600
    
    print(f"⏰ Ước tính thời gian còn lại: {estimated_hours:.1f} giờ")
    print(f"🎯 Dự kiến hoàn thành: {time.strftime('%H:%M', time.localtime(time.time() + estimated_seconds))}")

def create_progress_summary():
    """Tạo summary tiến trình"""
    accounts_dir = Path("config_dir/accounts")
    json_files = list(accounts_dir.glob("*.json"))
    
    # Phân tích theo project
    project_stats = {}
    for json_file in json_files:
        # Format: XX-YYY.json (project-sa)
        parts = json_file.stem.split('-')
        if len(parts) >= 2:
            project_idx = parts[0]
            if project_idx not in project_stats:
                project_stats[project_idx] = 0
            project_stats[project_idx] += 1
    
    print(f"\n📊 Progress by Project:")
    print("=" * 30)
    total_sas = 0
    for project_idx in sorted(project_stats.keys()):
        sa_count = project_stats[project_idx]
        total_sas += sa_count
        progress = (sa_count / 100) * 100
        print(f"Project {project_idx}: {sa_count:3d}/100 SAs ({progress:5.1f}%)")
    
    print(f"=" * 30)
    print(f"Total: {total_sas:4d}/1200 SAs ({(total_sas/1200)*100:5.1f}%)")
    print(f"Storage: {total_sas * 15}GB = {(total_sas * 15)/1024:.1f}TiB")

def main():
    print("🎯 17TiB Infrastructure Monitor")
    print("Chọn action:")
    print("1. Monitor progress real-time")
    print("2. Check current status")
    print("3. Check Google Cloud projects")
    print("4. Estimate completion time")
    
    choice = input("Nhập lựa chọn (1-4): ").strip()
    
    if choice == "1":
        try:
            monitor_progress()
        except KeyboardInterrupt:
            print("\n👋 Monitoring stopped")
    
    elif choice == "2":
        create_progress_summary()
    
    elif choice == "3":
        check_projects()
    
    elif choice == "4":
        estimate_completion()
    
    else:
        print("❌ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
