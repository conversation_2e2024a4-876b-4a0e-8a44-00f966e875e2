#!/usr/bin/env python3
"""
Setup gclone cho 17TiB infrastructure
"""

import subprocess
import json
import os
from pathlib import Path

def setup_gclone_config():
    """Setup gclone config với service accounts"""
    print("⚙️  Setup gclone config cho 17TiB...")
    
    accounts_dir = Path("config_dir/accounts")
    json_files = list(accounts_dir.glob("*.json"))
    
    if not json_files:
        print("❌ Không tìm thấy service account files")
        return False
    
    print(f"📁 Tìm thấy {len(json_files)} service account files")
    
    # Lấy service account đầu tiên
    first_sa = json_files[0]
    
    # Tạo gclone config
    print("🔧 Tạo gclone config...")
    
    try:
        # Xóa config cũ nếu có
        subprocess.run([
            './gclone/gclone', 'config', 'delete', 'sadrive'
        ], capture_output=True)
        
        # Tạo config mới
        config_cmd = [
            './gclone/gclone', 'config', 'create', 'sadrive', 'drive',
            '--drive-service-account-file', str(first_sa.absolute()),
            '--drive-service-account-file-path', str(accounts_dir.absolute()),
            '--non-interactive'
        ]
        
        result = subprocess.run(config_cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Gclone config đã được tạo")
            return True
        else:
            print(f"❌ Lỗi tạo config: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Timeout tạo config")
        return False
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def test_gclone_connection():
    """Test kết nối gclone"""
    print("🧪 Test kết nối gclone...")
    
    try:
        # Test list root
        result = subprocess.run([
            './gclone/gclone', 'lsd', 'sadrive:', '--max-depth', '1'
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Gclone kết nối thành công!")
            print("📋 Root directories:")
            print(result.stdout)
            return True
        else:
            print(f"❌ Lỗi kết nối: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Timeout test kết nối")
        return False
    except Exception as e:
        print(f"❌ Lỗi test: {e}")
        return False

def create_test_upload():
    """Tạo test upload"""
    print("📤 Test upload file...")
    
    # Tạo test file
    test_file = Path("17tb_test_file.txt")
    test_content = f"""🎉 17TiB SA Drive Test File

Created: {__import__('time').strftime('%Y-%m-%d %H:%M:%S')}
Infrastructure: 12 projects × 100 service accounts = 1200 SAs
Total Storage: 18,000GB = 17.6TiB

This file confirms that your 17TiB SA Drive is working!

Features:
- ✅ Automatic service account rotation
- ✅ Unlimited upload/download
- ✅ Mount as local filesystem
- ✅ Progress tracking
- ✅ Resume interrupted transfers

Next steps:
1. Upload your files: ./gclone/gclone copy myfile.txt sadrive:
2. Mount filesystem: ./gclone/gclone mount sadrive: ~/sa-drive-mount
3. Sync folders: ./gclone/gclone sync ./folder sadrive:backup/

Enjoy your 17TiB of free storage! 🚀
"""
    
    test_file.write_text(test_content)
    
    try:
        # Upload test file
        result = subprocess.run([
            './gclone/gclone', 'copy', str(test_file), 'sadrive:', '-v', '--progress'
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ Test upload thành công!")
            print("📋 Upload log:")
            print(result.stderr[-500:])  # Last 500 chars
            
            # Verify upload
            result = subprocess.run([
                './gclone/gclone', 'ls', 'sadrive:'
            ], capture_output=True, text=True, timeout=30)
            
            if test_file.name in result.stdout:
                print(f"✅ File {test_file.name} đã có trên drive!")
                return True
            else:
                print("⚠️  Upload thành công nhưng không thấy file")
                return False
        else:
            print(f"❌ Upload thất bại: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Upload timeout")
        return False
    except Exception as e:
        print(f"❌ Lỗi upload: {e}")
        return False

def create_17tb_scripts():
    """Tạo scripts cho 17TiB usage"""
    print("📝 Tạo 17TiB usage scripts...")
    
    # Upload script
    upload_script = """#!/bin/bash
# 17TiB SA Drive Upload Script
if [ $# -eq 0 ]; then
    echo "Usage: $0 <file_or_folder> [remote_path]"
    echo "Examples:"
    echo "  $0 myfile.txt"
    echo "  $0 myfolder/ backup/"
    exit 1
fi

REMOTE_PATH="${2:-}"
echo "📤 Uploading $1 to SA Drive (17TiB)..."
echo "🔄 Using automatic service account rotation..."

./gclone/gclone copy "$1" "sadrive:$REMOTE_PATH" \\
    --progress \\
    --transfers 3 \\
    --checkers 8 \\
    --drive-chunk-size 64M \\
    --drive-upload-cutoff 8M \\
    --stats 10s

echo "✅ Upload completed!"
"""
    
    with open("upload_17tb.sh", 'w') as f:
        f.write(upload_script)
    os.chmod("upload_17tb.sh", 0o755)
    
    # Download script
    download_script = """#!/bin/bash
# 17TiB SA Drive Download Script
if [ $# -lt 2 ]; then
    echo "Usage: $0 <remote_file> <local_destination>"
    echo "Examples:"
    echo "  $0 myfile.txt ./downloads/"
    echo "  $0 backup/myfolder/ ./restored/"
    exit 1
fi

echo "📥 Downloading $1 from SA Drive (17TiB)..."
echo "🔄 Using automatic service account rotation..."

./gclone/gclone copy "sadrive:$1" "$2" \\
    --progress \\
    --transfers 5 \\
    --checkers 8 \\
    --stats 10s

echo "✅ Download completed!"
"""
    
    with open("download_17tb.sh", 'w') as f:
        f.write(download_script)
    os.chmod("download_17tb.sh", 0o755)
    
    # Sync script
    sync_script = """#!/bin/bash
# 17TiB SA Drive Sync Script
if [ $# -lt 2 ]; then
    echo "Usage: $0 <local_folder> <remote_folder>"
    echo "Examples:"
    echo "  $0 ./documents/ backup/documents/"
    echo "  $0 ./photos/ photos/"
    exit 1
fi

echo "🔄 Syncing $1 to sadrive:$2..."
echo "📊 This will make source and destination identical"

./gclone/gclone sync "$1" "sadrive:$2" \\
    --progress \\
    --transfers 3 \\
    --checkers 8 \\
    --stats 10s \\
    --delete-during

echo "✅ Sync completed!"
"""
    
    with open("sync_17tb.sh", 'w') as f:
        f.write(sync_script)
    os.chmod("sync_17tb.sh", 0o755)
    
    # Mount script
    mount_script = """#!/bin/bash
# 17TiB SA Drive Mount Script
MOUNT_POINT="$HOME/sa-drive-17tb"
mkdir -p "$MOUNT_POINT"

echo "🗂️  Mounting 17TiB SA Drive to $MOUNT_POINT"
echo "📁 Access your files at: $MOUNT_POINT"
echo "⚠️  Press Ctrl+C to unmount"
echo ""

./gclone/gclone mount sadrive: "$MOUNT_POINT" \\
    --vfs-cache-mode writes \\
    --vfs-cache-max-size 2G \\
    --vfs-cache-max-age 1h \\
    --buffer-size 64M \\
    --dir-cache-time 5m \\
    --poll-interval 10s

echo "📤 Unmounted SA Drive"
"""
    
    with open("mount_17tb.sh", 'w') as f:
        f.write(mount_script)
    os.chmod("mount_17tb.sh", 0o755)
    
    # List script
    list_script = """#!/bin/bash
# 17TiB SA Drive List Script
echo "📋 Files on 17TiB SA Drive:"
echo "=========================="

if [ $# -eq 0 ]; then
    ./gclone/gclone ls sadrive: --max-depth 2
else
    ./gclone/gclone ls "sadrive:$1"
fi
"""
    
    with open("list_17tb.sh", 'w') as f:
        f.write(list_script)
    os.chmod("list_17tb.sh", 0o755)
    
    print("✅ Đã tạo 17TiB scripts:")
    print("  - upload_17tb.sh <file> [remote_path]")
    print("  - download_17tb.sh <remote_file> <local>")
    print("  - sync_17tb.sh <local_folder> <remote_folder>")
    print("  - mount_17tb.sh")
    print("  - list_17tb.sh [path]")

def create_17tb_guide():
    """Tạo hướng dẫn sử dụng 17TiB"""
    guide = """# 🚀 17TiB SA Drive Usage Guide

## 📊 Your Infrastructure
- **Total Storage**: 18,000GB = 17.6TiB
- **Service Accounts**: 1,200 SAs across 12 projects
- **Auto Rotation**: Gclone automatically switches SAs when quota exceeded
- **Unlimited Transfers**: No daily limits with SA rotation

## 🎯 Quick Start

### Upload Files
```bash
# Upload single file
./upload_17tb.sh myfile.txt

# Upload to specific folder
./upload_17tb.sh myfile.txt backup/

# Upload entire folder
./upload_17tb.sh ./myfolder/
```

### Download Files
```bash
# Download single file
./download_17tb.sh myfile.txt ./downloads/

# Download entire folder
./download_17tb.sh backup/myfolder/ ./restored/
```

### Sync Folders
```bash
# Sync local folder to drive
./sync_17tb.sh ./documents/ backup/documents/

# Keep folders identical
./sync_17tb.sh ./photos/ photos/
```

### Mount as Filesystem
```bash
# Mount drive as local folder
./mount_17tb.sh

# Access files at ~/sa-drive-17tb/
# Use like any local folder!
```

### List Files
```bash
# List root directory
./list_17tb.sh

# List specific folder
./list_17tb.sh backup/
```

## 🔧 Advanced Usage

### Direct gclone Commands
```bash
# Copy with progress
./gclone/gclone copy localfile.txt sadrive: --progress

# Sync with stats
./gclone/gclone sync ./folder sadrive:backup/ --stats 30s

# Mount with caching
./gclone/gclone mount sadrive: ~/mount --vfs-cache-mode writes

# Check storage usage
./gclone/gclone about sadrive:
```

### Performance Optimization
```bash
# Fast upload (multiple transfers)
./gclone/gclone copy bigfile.zip sadrive: \\
    --transfers 8 \\
    --checkers 16 \\
    --drive-chunk-size 128M

# Resume interrupted transfers
./gclone/gclone copy ./bigfolder sadrive:backup/ \\
    --progress \\
    --stats 10s \\
    --retries 3
```

## 📈 Storage Management

### Check Usage
```bash
# Check total usage
./gclone/gclone about sadrive:

# List large files
./gclone/gclone ls sadrive: --max-depth 3 -l

# Find duplicates
./gclone/gclone dedupe sadrive:
```

### Organize Files
```bash
# Create folders
./gclone/gclone mkdir sadrive:backup/2024/

# Move files
./gclone/gclone move sadrive:oldfile.txt sadrive:archive/

# Delete files
./gclone/gclone delete sadrive:unwanted.txt
```

## 🎉 You Now Have 17TiB!

Your SA Drive infrastructure is complete and ready for:
- ✅ Unlimited file uploads/downloads
- ✅ Automatic service account rotation
- ✅ Mount as local filesystem
- ✅ Sync entire folders
- ✅ Resume interrupted transfers
- ✅ Progress tracking and stats

**Enjoy your 17TiB of free Google Drive storage! 🚀**
"""
    
    with open("17TB_USAGE_GUIDE.md", 'w') as f:
        f.write(guide)
    
    print("📖 Đã tạo 17TB_USAGE_GUIDE.md")

def main():
    print("⚙️  17TiB SA Drive Setup & Configuration")
    print("=" * 45)
    
    # Wait for service accounts
    accounts_dir = Path("config_dir/accounts")
    json_files = list(accounts_dir.glob("*.json"))
    
    if len(json_files) < 10:
        print(f"⏳ Đang chờ service accounts... (hiện có {len(json_files)})")
        print("💡 Script này sẽ setup gclone khi có đủ service accounts")
        return
    
    print(f"📁 Tìm thấy {len(json_files)} service accounts")
    
    # Setup gclone
    if setup_gclone_config():
        print("✅ Gclone config setup thành công")
        
        # Test connection
        if test_gclone_connection():
            print("✅ Kết nối thành công")
            
            # Test upload
            if create_test_upload():
                print("✅ Test upload thành công")
            
            # Tạo scripts
            create_17tb_scripts()
            
            # Tạo guide
            create_17tb_guide()
            
            print("\n🎉 17TiB SA Drive đã sẵn sàng!")
            print("📊 Total Storage: 17.6TiB")
            print("🚀 Usage: ./upload_17tb.sh <file>")
            print("📖 Guide: 17TB_USAGE_GUIDE.md")
        else:
            print("❌ Kết nối thất bại")
    else:
        print("❌ Setup gclone thất bại")

if __name__ == "__main__":
    main()
