#!/usr/bin/env python3
"""
SA Drive Setup Script - Tạo 17TiB storage với Service Accounts
Tạo 12 projects × 100 service accounts = 1200 SAs = 17TiB storage
"""

import os
import json
import subprocess
import time
from pathlib import Path

class SADriveSetup:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.accounts_dir = self.base_dir / "config_dir" / "accounts"
        self.accounts_dir.mkdir(parents=True, exist_ok=True)
        
        # Tạo 12 projects để tối đa hóa service accounts
        self.project_count = 12
        self.sa_per_project = 100
        self.total_sas = self.project_count * self.sa_per_project
        
        print(f"🎯 Mục tiêu: {self.total_sas} Service Accounts = {self.total_sas * 15}GB = {self.total_sas * 15 / 1024:.1f}TiB")
    
    def check_gcloud_auth(self):
        """Kiểm tra xem đã đăng nhập Google Cloud chưa"""
        try:
            result = subprocess.run(['gcloud', 'auth', 'list'], 
                                  capture_output=True, text=True, check=True)
            if "ACTIVE" in result.stdout:
                print("✅ Đã đăng nhập Google Cloud")
                return True
            else:
                print("❌ Chưa đăng nhập Google Cloud")
                return False
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Google Cloud CLI chưa được cài đặt")
            return False
    
    def install_gcloud_cli(self):
        """Hướng dẫn cài đặt Google Cloud CLI"""
        print("\n📥 Cài đặt Google Cloud CLI:")
        print("1. Truy cập: https://cloud.google.com/sdk/docs/install")
        print("2. Tải và cài đặt Google Cloud CLI")
        print("3. Chạy: gcloud init")
        print("4. Đăng nhập với Gmail account của bạn")
        
    def create_projects(self):
        """Tạo 12 Google Cloud Projects"""
        print(f"\n🏗️  Tạo {self.project_count} Google Cloud Projects...")
        
        projects = []
        for i in range(1, self.project_count + 1):
            project_id = f"sa-drive-project-{i:02d}-{int(time.time())}"
            project_name = f"SA Drive Project {i}"
            
            print(f"Tạo project {i}/{self.project_count}: {project_id}")
            
            # Tạo project
            cmd = [
                'gcloud', 'projects', 'create', project_id,
                '--name', project_name,
                '--set-as-default'
            ]
            
            try:
                subprocess.run(cmd, check=True, capture_output=True)
                projects.append(project_id)
                print(f"✅ Đã tạo project: {project_id}")
                
                # Enable Drive API
                subprocess.run([
                    'gcloud', 'services', 'enable', 'drive.googleapis.com',
                    '--project', project_id
                ], check=True, capture_output=True)
                
                time.sleep(2)  # Tránh rate limiting
                
            except subprocess.CalledProcessError as e:
                print(f"❌ Lỗi tạo project {project_id}: {e}")
                
        return projects
    
    def create_service_accounts(self, project_id):
        """Tạo 100 service accounts cho một project"""
        print(f"\n👥 Tạo {self.sa_per_project} Service Accounts cho {project_id}...")
        
        service_accounts = []
        for i in range(self.sa_per_project):
            sa_name = f"sa-{i:03d}"
            sa_email = f"{sa_name}@{project_id}.iam.gserviceaccount.com"
            
            try:
                # Tạo service account
                subprocess.run([
                    'gcloud', 'iam', 'service-accounts', 'create', sa_name,
                    '--display-name', f'Service Account {i}',
                    '--project', project_id
                ], check=True, capture_output=True)
                
                # Tạo và tải key
                key_file = self.accounts_dir / f"{project_id}-{sa_name}.json"
                subprocess.run([
                    'gcloud', 'iam', 'service-accounts', 'keys', 'create',
                    str(key_file),
                    '--iam-account', sa_email,
                    '--project', project_id
                ], check=True, capture_output=True)
                
                service_accounts.append(sa_email)
                
                if (i + 1) % 10 == 0:
                    print(f"  ✅ Đã tạo {i + 1}/{self.sa_per_project} SAs")
                
                time.sleep(0.5)  # Tránh rate limiting
                
            except subprocess.CalledProcessError as e:
                print(f"❌ Lỗi tạo SA {sa_name}: {e}")
        
        return service_accounts
    
    def setup_drive_folder(self):
        """Hướng dẫn tạo Google Drive folder"""
        print("\n📁 Thiết lập Google Drive Folder:")
        print("1. Truy cập https://drive.google.com")
        print("2. Tạo folder mới tên 'SA-Drive'")
        print("3. Click chuột phải → 'Get link' → Copy link")
        print("4. Lấy ID từ link (phần sau /folders/)")
        print("5. Cập nhật parent_id trong config.json")
        
    def generate_setup_commands(self):
        """Tạo file script commands để chạy thủ công"""
        commands_file = self.base_dir / "setup_commands.sh"
        
        with open(commands_file, 'w') as f:
            f.write("#!/bin/bash\n")
            f.write("# SA Drive Setup Commands\n")
            f.write("# Chạy từng lệnh một cách thủ công\n\n")
            
            f.write("# 1. Đăng nhập Google Cloud\n")
            f.write("gcloud auth login\n")
            f.write("gcloud config set project [YOUR_DEFAULT_PROJECT]\n\n")
            
            f.write("# 2. Tạo projects và service accounts\n")
            for i in range(1, self.project_count + 1):
                project_id = f"sa-drive-project-{i:02d}"
                f.write(f"\n# Project {i}\n")
                f.write(f"gcloud projects create {project_id} --name='SA Drive Project {i}'\n")
                f.write(f"gcloud services enable drive.googleapis.com --project {project_id}\n")
                
                for j in range(self.sa_per_project):
                    sa_name = f"sa-{j:03d}"
                    f.write(f"gcloud iam service-accounts create {sa_name} --project {project_id}\n")
                    f.write(f"gcloud iam service-accounts keys create config_dir/accounts/{project_id}-{sa_name}.json --iam-account {sa_name}@{project_id}.iam.gserviceaccount.com --project {project_id}\n")
        
        os.chmod(commands_file, 0o755)
        print(f"📝 Đã tạo file commands: {commands_file}")
    
    def run_setup(self):
        """Chạy setup hoàn chỉnh"""
        print("🚀 Bắt đầu thiết lập SA Drive cho 17TiB storage...")
        
        if not self.check_gcloud_auth():
            self.install_gcloud_cli()
            print("\n⚠️  Vui lòng cài đặt và đăng nhập Google Cloud CLI trước")
            return
        
        # Tạo file commands để backup
        self.generate_setup_commands()
        
        # Tạo projects
        projects = self.create_projects()
        
        # Tạo service accounts cho mỗi project
        all_service_accounts = []
        for project_id in projects:
            sas = self.create_service_accounts(project_id)
            all_service_accounts.extend(sas)
        
        print(f"\n🎉 Hoàn thành! Đã tạo {len(all_service_accounts)} Service Accounts")
        print(f"📊 Tổng storage: {len(all_service_accounts) * 15}GB = {len(all_service_accounts) * 15 / 1024:.1f}TiB")
        
        self.setup_drive_folder()

if __name__ == "__main__":
    setup = SADriveSetup()
    setup.run_setup()
