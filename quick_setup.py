#!/usr/bin/env python3
"""
Quick SA Drive Setup - Tạo một vài Service Accounts để test trước
"""

import os
import json
import subprocess
import time
from pathlib import Path

def check_gcloud():
    """Kiểm tra Google Cloud CLI"""
    try:
        result = subprocess.run(['gcloud', '--version'], capture_output=True, text=True)
        print("✅ Google Cloud CLI đã cài đặt")
        return True
    except FileNotFoundError:
        print("❌ Google Cloud CLI chưa được cài đặt")
        return False

def install_gcloud_macos():
    """Cài đặt Google Cloud CLI trên macOS"""
    print("📥 Cài đặt Google Cloud CLI...")
    
    # Kiểm tra Homebrew
    try:
        subprocess.run(['brew', '--version'], capture_output=True, check=True)
        print("🍺 Sử dụng Homebrew để cài đặt...")
        subprocess.run(['brew', 'install', 'google-cloud-sdk'], check=True)
        return True
    except (FileNotFoundError, subprocess.CalledProcessError):
        print("❌ Homebrew không có. Vui lòng cài đặt thủ công:")
        print("1. Truy cập: https://cloud.google.com/sdk/docs/install-sdk")
        print("2. Tải Google Cloud CLI cho macOS")
        print("3. Chạy installer")
        return False

def create_sample_service_accounts():
    """Tạo một vài service accounts để test"""
    accounts_dir = Path("config_dir/accounts")
    accounts_dir.mkdir(parents=True, exist_ok=True)
    
    print("🧪 Tạo Service Accounts mẫu để test...")
    
    # Tạo 5 service accounts mẫu
    for i in range(5):
        sa_data = {
            "type": "service_account",
            "project_id": f"test-project-{i+1}",
            "private_key_id": f"test-key-id-{i+1}",
            "private_key": "-----BEGIN PRIVATE KEY-----\nTEST_PRIVATE_KEY_CONTENT\n-----END PRIVATE KEY-----\n",
            "client_email": f"test-sa-{i+1}@test-project-{i+1}.iam.gserviceaccount.com",
            "client_id": f"12345678901234567890{i+1}",
            "auth_uri": "https://accounts.google.com/o/oauth2/auth",
            "token_uri": "https://oauth2.googleapis.com/token",
            "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
            "client_x509_cert_url": f"https://www.googleapis.com/robot/v1/metadata/x509/test-sa-{i+1}%40test-project-{i+1}.iam.gserviceaccount.com"
        }
        
        with open(accounts_dir / f"{i}.json", 'w') as f:
            json.dump(sa_data, f, indent=2)
    
    print(f"✅ Đã tạo 5 service accounts mẫu trong {accounts_dir}")
    return True

def main():
    print("🚀 SA Drive Quick Setup")
    print("=" * 50)
    
    # Kiểm tra và cài đặt Google Cloud CLI
    if not check_gcloud():
        if install_gcloud_macos():
            print("✅ Đã cài đặt Google Cloud CLI")
        else:
            print("⚠️  Vui lòng cài đặt Google Cloud CLI thủ công")
            return
    
    # Tạo service accounts mẫu để test
    create_sample_service_accounts()
    
    print("\n📋 Các bước tiếp theo:")
    print("1. Chạy: gcloud auth login")
    print("2. Tạo project: gcloud projects create your-project-id")
    print("3. Enable Drive API: gcloud services enable drive.googleapis.com")
    print("4. Tạo service accounts thật với script setup_sa_drive.py")
    print("5. Test SA Drive: sadrive details")

if __name__ == "__main__":
    main()
