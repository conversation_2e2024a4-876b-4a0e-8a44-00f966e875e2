#!/usr/bin/env python3
"""
🚀 SA Drive - Start Here Script
Hướng dẫn từng bước để đạt 17TiB Google Drive Storage
"""

import subprocess
import sys
import os
from pathlib import Path

def print_banner():
    print("🚀 SA Drive Setup - 17TiB Google Drive Storage")
    print("=" * 55)
    print("🎯 Mục tiêu: 12 projects × 100 SAs = 1200 SAs = 17TiB")
    print("=" * 55)

def check_requirements():
    """Kiểm tra các yêu cầu cần thiết"""
    print("\n📋 Kiểm tra yêu cầu...")
    
    requirements = {
        "Python 3": sys.version_info >= (3, 6),
        "SA Drive": True,
        "Google Cloud CLI": True,
        "Gclone": True
    }
    
    # Kiểm tra SA Drive
    try:
        subprocess.run(['sadrive', '--help'], capture_output=True, check=True)
        requirements["SA Drive"] = True
    except (subprocess.CalledProcessError, FileNotFoundError):
        requirements["SA Drive"] = False
    
    # Kiểm tra Google Cloud CLI
    try:
        subprocess.run(['gcloud', '--version'], capture_output=True, check=True)
        requirements["Google Cloud CLI"] = True
    except (subprocess.CalledProcessError, FileNotFoundError):
        requirements["Google Cloud CLI"] = False
    
    # Kiểm tra Gclone
    gclone_path = Path("gclone/gclone")
    requirements["Gclone"] = gclone_path.exists()
    
    # Hiển thị kết quả
    all_good = True
    for req, status in requirements.items():
        icon = "✅" if status else "❌"
        print(f"  {icon} {req}")
        if not status:
            all_good = False
    
    return all_good

def show_next_steps():
    """Hiển thị các bước tiếp theo"""
    print("\n📋 Các bước tiếp theo:")
    print()
    
    print("1️⃣  Đăng nhập Google Cloud:")
    print("   gcloud auth login")
    print()
    
    print("2️⃣  Chọn setup:")
    print("   🧪 Test Setup (3 SAs = 45GB):")
    print("      python3 test_with_real_sa.py")
    print()
    print("   🚀 Full Setup (1200 SAs = 17TiB):")
    print("      python3 create_real_service_accounts.py")
    print()
    
    print("3️⃣  Thiết lập Google Drive:")
    print("   - Tạo folder 'SA-Drive' trên drive.google.com")
    print("   - Lấy folder ID từ URL")
    print("   - Cập nhật parent_id trong config_dir/config.json")
    print()
    
    print("4️⃣  Test SA Drive:")
    print("   sadrive details")
    print("   sadrive upload <file>")
    print()

def interactive_setup():
    """Setup tương tác"""
    print("\n🎮 Interactive Setup")
    print("=" * 25)
    
    print("Bạn muốn làm gì?")
    print("1. Test với 3 Service Accounts (45GB)")
    print("2. Tạo Full Setup 17TiB (1200 SAs)")
    print("3. Xem hướng dẫn chi tiết")
    print("4. Kiểm tra SA Drive hiện tại")
    print("5. Thoát")
    
    choice = input("\nNhập lựa chọn (1-5): ").strip()
    
    if choice == "1":
        print("\n🧪 Bắt đầu Test Setup...")
        subprocess.run([sys.executable, "test_with_real_sa.py"])
    
    elif choice == "2":
        print("\n🚀 Bắt đầu Full Setup...")
        subprocess.run([sys.executable, "create_real_service_accounts.py"])
    
    elif choice == "3":
        print("\n📖 Mở hướng dẫn...")
        if Path("SETUP_GUIDE.md").exists():
            if sys.platform == "darwin":  # macOS
                subprocess.run(["open", "SETUP_GUIDE.md"])
            else:
                print("📝 Xem file: SETUP_GUIDE.md")
        else:
            print("❌ Không tìm thấy SETUP_GUIDE.md")
    
    elif choice == "4":
        print("\n📊 Kiểm tra SA Drive...")
        subprocess.run(["sadrive", "details"])
    
    elif choice == "5":
        print("👋 Tạm biệt!")
        return
    
    else:
        print("❌ Lựa chọn không hợp lệ")

def main():
    print_banner()
    
    # Kiểm tra yêu cầu
    if not check_requirements():
        print("\n❌ Một số yêu cầu chưa được đáp ứng")
        print("📋 Vui lòng cài đặt các thành phần còn thiếu")
        return
    
    print("\n✅ Tất cả yêu cầu đã được đáp ứng!")
    
    # Kiểm tra đăng nhập Google Cloud
    try:
        result = subprocess.run(['gcloud', 'auth', 'list', '--filter=status:ACTIVE'], 
                              capture_output=True, text=True, check=True)
        if result.stdout.strip():
            print("✅ Đã đăng nhập Google Cloud")
            interactive_setup()
        else:
            print("❌ Chưa đăng nhập Google Cloud")
            print("📋 Vui lòng chạy: gcloud auth login")
    except subprocess.CalledProcessError:
        print("❌ Lỗi kiểm tra Google Cloud")
        show_next_steps()

if __name__ == "__main__":
    main()
