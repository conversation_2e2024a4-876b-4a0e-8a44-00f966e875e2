#!/usr/bin/env python3
"""
17TiB SA Drive Final Summary & Status
"""

import subprocess
import json
import time
from pathlib import Path

def get_infrastructure_status():
    """Lấy trạng thái infrastructure hiện tại"""
    print("📊 17TiB SA Drive Infrastructure Status")
    print("=" * 50)
    
    # Kiểm tra service accounts
    accounts_dir = Path("config_dir/accounts")
    json_files = list(accounts_dir.glob("*.json"))
    
    print(f"📁 Service Accounts: {len(json_files)}")
    print(f"💾 Current Storage: {len(json_files) * 15}GB = {(len(json_files) * 15)/1024:.1f}TiB")
    
    # Phân tích theo project
    project_stats = {}
    for json_file in json_files:
        parts = json_file.stem.split('-')
        if len(parts) >= 2:
            project_idx = parts[0]
            if project_idx not in project_stats:
                project_stats[project_idx] = 0
            project_stats[project_idx] += 1
    
    print(f"\n📋 Projects with Service Accounts:")
    for project_idx in sorted(project_stats.keys()):
        sa_count = project_stats[project_idx]
        print(f"  Project {project_idx}: {sa_count} SAs")
    
    return len(json_files), project_stats

def check_gclone_status():
    """Kiểm tra trạng thái gclone"""
    print(f"\n🔧 Gclone Status:")
    
    # Kiểm tra gclone binary
    gclone_path = Path("gclone/gclone")
    if gclone_path.exists():
        print("✅ Gclone binary: Available")
        
        # Kiểm tra config
        try:
            result = subprocess.run([
                './gclone/gclone', 'config', 'show'
            ], capture_output=True, text=True, timeout=10)
            
            if 'sadrive' in result.stdout or 'gdrive' in result.stdout:
                print("✅ Gclone config: Configured")
                return True
            else:
                print("⚠️  Gclone config: Not configured")
                return False
        except:
            print("❌ Gclone config: Error checking")
            return False
    else:
        print("❌ Gclone binary: Not found")
        return False

def create_usage_examples():
    """Tạo ví dụ sử dụng"""
    print(f"\n🚀 Usage Examples:")
    print("=" * 30)
    
    examples = [
        ("Upload file", "./gclone/gclone copy myfile.txt sadrive:"),
        ("Upload folder", "./gclone/gclone copy ./myfolder/ sadrive:backup/"),
        ("Download file", "./gclone/gclone copy sadrive:myfile.txt ./downloads/"),
        ("List files", "./gclone/gclone ls sadrive:"),
        ("Mount drive", "./gclone/gclone mount sadrive: ~/sa-drive-mount"),
        ("Sync folder", "./gclone/gclone sync ./local/ sadrive:remote/"),
        ("Check storage", "./gclone/gclone about sadrive:")
    ]
    
    for desc, cmd in examples:
        print(f"📌 {desc}:")
        print(f"   {cmd}")
        print()

def calculate_potential():
    """Tính toán potential storage"""
    current_sas, project_stats = get_infrastructure_status()
    
    print(f"\n📈 Storage Potential:")
    print("=" * 25)
    
    # Current
    current_storage_gb = current_sas * 15
    current_storage_tb = current_storage_gb / 1024
    print(f"🔹 Current: {current_sas} SAs = {current_storage_gb}GB = {current_storage_tb:.1f}TiB")
    
    # Per project max
    projects_count = len(project_stats)
    if projects_count > 0:
        max_per_project = projects_count * 100 * 15  # 100 SAs per project
        print(f"🔹 Current Projects Max: {projects_count} × 100 SAs = {max_per_project}GB = {max_per_project/1024:.1f}TiB")
    
    # Full potential
    full_potential = 12 * 100 * 15  # 12 projects × 100 SAs
    print(f"🔹 Full Potential: 12 × 100 SAs = {full_potential}GB = {full_potential/1024:.1f}TiB")
    
    # Multiple Gmail accounts
    print(f"🔹 Multiple Gmail: Unlimited (each Gmail = {full_potential/1024:.1f}TiB)")

def create_next_steps():
    """Tạo next steps"""
    current_sas, project_stats = get_infrastructure_status()
    gclone_ready = check_gclone_status()
    
    print(f"\n📋 Next Steps:")
    print("=" * 20)
    
    if current_sas == 0:
        print("1. ⏳ Đợi infrastructure creation hoàn thành")
        print("2. 🔧 Setup gclone config")
        print("3. 🧪 Test upload/download")
    
    elif current_sas < 100:
        print("1. ⏳ Đợi thêm service accounts")
        if not gclone_ready:
            print("2. 🔧 Setup gclone: python3 setup_gclone_17tb.py")
        print("3. 🧪 Test với SAs hiện có")
    
    elif current_sas >= 100:
        if not gclone_ready:
            print("1. 🔧 Setup gclone: python3 setup_gclone_17tb.py")
        print("2. 🚀 Bắt đầu sử dụng 17TiB storage!")
        print("3. 📖 Đọc 17TB_USAGE_GUIDE.md")
    
    # Scale up options
    if len(project_stats) < 12:
        remaining_projects = 12 - len(project_stats)
        potential_sas = remaining_projects * 100
        potential_storage = potential_sas * 15
        print(f"\n🚀 Scale Up Options:")
        print(f"   - Tạo thêm {remaining_projects} projects = +{potential_sas} SAs = +{potential_storage/1024:.1f}TiB")

def main():
    print("🎯 17TiB SA Drive Final Summary")
    print("=" * 40)
    
    # Status overview
    current_sas, project_stats = get_infrastructure_status()
    gclone_ready = check_gclone_status()
    
    # Calculate potential
    calculate_potential()
    
    # Usage examples
    if current_sas > 0:
        create_usage_examples()
    
    # Next steps
    create_next_steps()
    
    # Final message
    print(f"\n🎉 SA Drive Infrastructure Summary:")
    print("=" * 40)
    
    if current_sas >= 1200:
        print("✅ FULL 17TiB INFRASTRUCTURE COMPLETE!")
        print("🚀 You now have 17.6TiB of Google Drive storage!")
    elif current_sas >= 100:
        print(f"✅ WORKING INFRASTRUCTURE: {(current_sas*15)/1024:.1f}TiB")
        print("🚀 Ready to use with scaling potential!")
    elif current_sas > 0:
        print(f"⏳ INFRASTRUCTURE IN PROGRESS: {(current_sas*15)/1024:.1f}TiB")
        print("🔄 Continue building to reach 17TiB")
    else:
        print("⏳ INFRASTRUCTURE CREATION STARTING...")
        print("🔄 Please wait for service accounts to be created")
    
    print(f"\n📊 Current: {current_sas}/1200 SAs ({(current_sas/1200)*100:.1f}%)")
    print(f"💾 Storage: {current_sas*15}GB = {(current_sas*15)/1024:.1f}TiB")
    
    if gclone_ready:
        print("✅ Gclone: Ready")
    else:
        print("⚠️  Gclone: Needs setup")

if __name__ == "__main__":
    main()
