# 🚀 SA Drive Scaling Guide

## Current Setup
- ✅ 4 Service Accounts = 60GB storage
- ✅ Gclone configured và working
- ✅ Alternative scripts created

## Scale Up Options

### Option 1: Add more SAs to current project (up to 100)
```bash
# Tạo thêm service accounts
for i in {5..20}; do
    gcloud iam service-accounts create sadrive$(printf "%02d" $i) \
        --display-name "SA Drive $i" \
        --project sa-drive-simple-**********
    
    gcloud iam service-accounts keys create config_dir/accounts/$i.json \
        --iam-account sadrive$(printf "%02d" $i)@sa-drive-simple-**********.iam.gserviceaccount.com \
        --project sa-drive-simple-**********
    
    sleep 3
done
```

### Option 2: Create more projects (up to 12 per Gmail)
```bash
# Tạo project mới
gcloud projects create sa-drive-project-02-$(date +%s) --name "SA Drive Project 2"
# Repeat service account creation process
```

### Option 3: Use multiple Gmail accounts
- Mỗi Gmail = 12 projects × 100 SAs = 1200 SAs = 17TiB
- Multiple Gmail accounts = Unlimited storage

## Usage Commands

### Upload
```bash
./sa_upload.sh myfile.txt
```

### Download
```bash
./sa_download.sh "remote_file.txt" ./downloads/
```

### List files
```bash
./sa_list.sh
```

### Direct gclone
```bash
./gclone/gclone copy localfile.txt gdrive:
./gclone/gclone mount gdrive: ~/sa-drive-mount
```

## Storage Calculation
- Current: 4 SAs × 15GB = 60GB
- Max per project: 100 SAs × 15GB = 1.5TB
- Max per Gmail: 12 projects × 1.5TB = 18TB
- Unlimited: Multiple Gmail accounts

## Next Steps
1. Test current setup với upload/download
2. Scale up theo nhu cầu
3. Tạo thêm projects khi cần
4. Monitor quota usage
