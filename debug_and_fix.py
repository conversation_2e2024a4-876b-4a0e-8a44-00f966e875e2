#!/usr/bin/env python3
"""
Debug và fix lỗi tạo Service Account keys
"""

import subprocess
import time
import json
from pathlib import Path

def check_project_status(project_id):
    """Kiểm tra trạng thái project"""
    print(f"🔍 Kiểm tra project: {project_id}")
    
    try:
        # Kiểm tra project info
        result = subprocess.run([
            'gcloud', 'projects', 'describe', project_id
        ], capture_output=True, text=True, check=True)
        
        print("✅ Project tồn tại")
        
        # Kiểm tra billing
        result = subprocess.run([
            'gcloud', 'billing', 'projects', 'describe', project_id
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Billing đã được enable")
        else:
            print("⚠️  Billing chưa được enable")
            return False
        
        # Kiểm tra APIs
        result = subprocess.run([
            'gcloud', 'services', 'list', '--enabled', '--project', project_id
        ], capture_output=True, text=True, check=True)
        
        if 'drive.googleapis.com' in result.stdout:
            print("✅ Drive API đã được enable")
        else:
            print("❌ Drive API chưa được enable")
            return False
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi kiểm tra project: {e}")
        return False

def enable_billing(project_id):
    """Enable billing cho project"""
    print(f"💳 Enable billing cho {project_id}...")
    
    try:
        # Lấy danh sách billing accounts
        result = subprocess.run([
            'gcloud', 'billing', 'accounts', 'list'
        ], capture_output=True, text=True, check=True)
        
        if "ACCOUNT_ID" not in result.stdout:
            print("❌ Không có billing account nào")
            print("📋 Vui lòng:")
            print("1. Truy cập https://console.cloud.google.com/billing")
            print("2. Tạo billing account (có thể dùng free tier)")
            print("3. Thử lại")
            return False
        
        # Lấy billing account ID đầu tiên
        lines = result.stdout.strip().split('\n')[1:]  # Skip header
        if lines:
            billing_account = lines[0].split()[0]
            
            # Link billing account với project
            subprocess.run([
                'gcloud', 'billing', 'projects', 'link', project_id,
                '--billing-account', billing_account
            ], check=True, capture_output=True)
            
            print(f"✅ Đã link billing account: {billing_account}")
            return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi enable billing: {e}")
        return False

def create_sa_with_retry(project_id, sa_name, key_file, max_retries=3):
    """Tạo service account với retry logic"""
    sa_email = f"{sa_name}@{project_id}.iam.gserviceaccount.com"
    
    for attempt in range(max_retries):
        try:
            # Tạo service account
            subprocess.run([
                'gcloud', 'iam', 'service-accounts', 'create', sa_name,
                '--display-name', f'SA Drive {sa_name}',
                '--project', project_id
            ], check=True, capture_output=True, text=True)
            
            # Đợi một chút để SA được tạo hoàn toàn
            time.sleep(2)
            
            # Tạo key
            subprocess.run([
                'gcloud', 'iam', 'service-accounts', 'keys', 'create',
                str(key_file),
                '--iam-account', sa_email,
                '--project', project_id
            ], check=True, capture_output=True, text=True)
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Attempt {attempt + 1} failed for {sa_name}: {e}")
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 5  # 5, 10, 15 seconds
                print(f"⏳ Đợi {wait_time}s trước khi thử lại...")
                time.sleep(wait_time)
            else:
                return False
    
    return False

def fix_current_project():
    """Fix project hiện tại đang có lỗi"""
    project_id = "sa-drive-01-443518-jjet"
    
    print(f"🔧 Fix project: {project_id}")
    
    # Kiểm tra và fix project
    if not check_project_status(project_id):
        if not enable_billing(project_id):
            return False
    
    # Set project
    subprocess.run(['gcloud', 'config', 'set', 'project', project_id], 
                 check=True, capture_output=True)
    
    # Enable APIs cần thiết
    apis = [
        'drive.googleapis.com',
        'iam.googleapis.com',
        'cloudresourcemanager.googleapis.com'
    ]
    
    for api in apis:
        print(f"🔧 Enable {api}...")
        try:
            subprocess.run([
                'gcloud', 'services', 'enable', api, '--project', project_id
            ], check=True, capture_output=True)
        except subprocess.CalledProcessError:
            print(f"⚠️  Không thể enable {api}")
    
    # Đợi APIs được enable
    print("⏳ Đợi APIs được enable...")
    time.sleep(30)
    
    # Tạo lại service accounts
    accounts_dir = Path("config_dir/accounts")
    
    print(f"👥 Tạo lại service accounts cho {project_id}...")
    success_count = 0
    
    for i in range(10):  # Tạo 10 SAs để test
        sa_name = f"sa-{i:03d}"
        key_file = accounts_dir / f"01-{i:03d}.json"
        
        if create_sa_with_retry(project_id, sa_name, key_file):
            success_count += 1
            print(f"  ✅ Đã tạo {sa_name}")
        else:
            print(f"  ❌ Không thể tạo {sa_name}")
        
        # Nghỉ giữa các SAs
        time.sleep(3)
    
    print(f"\n🎉 Đã tạo thành công {success_count}/10 service accounts")
    
    if success_count >= 5:
        print("✅ Project hoạt động tốt, có thể tiếp tục")
        return True
    else:
        print("❌ Project có vấn đề, cần kiểm tra thêm")
        return False

def create_smaller_setup():
    """Tạo setup nhỏ hơn để test"""
    print("🧪 Tạo Small Setup: 3 projects × 20 SAs = 60 SAs (900GB)")
    
    # Xóa service accounts cũ
    accounts_dir = Path("config_dir/accounts")
    for old_file in accounts_dir.glob("*.json"):
        old_file.unlink()
    
    all_sas = []
    
    for project_idx in range(1, 4):  # 3 projects
        project_id = f"sa-drive-small-{project_idx:02d}-{int(time.time())}"
        
        print(f"\n🏗️  Tạo Project {project_idx}: {project_id}")
        
        try:
            # Tạo project
            subprocess.run([
                'gcloud', 'projects', 'create', project_id,
                '--name', f'SA Drive Small {project_idx}'
            ], check=True, capture_output=True)
            
            # Set project
            subprocess.run(['gcloud', 'config', 'set', 'project', project_id], 
                         check=True, capture_output=True)
            
            # Enable billing nếu cần
            enable_billing(project_id)
            
            # Enable APIs
            subprocess.run([
                'gcloud', 'services', 'enable', 
                'drive.googleapis.com',
                'iam.googleapis.com'
            ], check=True, capture_output=True)
            
            time.sleep(10)  # Đợi APIs
            
            # Tạo 20 SAs
            for i in range(20):
                sa_name = f"sa-{i:03d}"
                key_file = accounts_dir / f"{project_idx:02d}-{i:03d}.json"
                
                if create_sa_with_retry(project_id, sa_name, key_file):
                    all_sas.append(f"{sa_name}@{project_id}.iam.gserviceaccount.com")
                
                if (i + 1) % 5 == 0:
                    print(f"  ✅ Đã tạo {i + 1}/20 SAs")
                
                time.sleep(2)
            
            print(f"✅ Hoàn thành project {project_id}")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Lỗi tạo project {project_id}: {e}")
    
    print(f"\n🎉 Small Setup hoàn thành: {len(all_sas)} SAs = {len(all_sas) * 15}GB")
    return all_sas

def main():
    print("🔧 Debug và Fix SA Drive Setup")
    print("=" * 40)
    
    print("Chọn action:")
    print("1. Fix project hiện tại (sa-drive-01-443518-jjet)")
    print("2. Tạo Small Setup (3 projects × 20 SAs = 900GB)")
    print("3. Kiểm tra billing và permissions")
    
    choice = input("Nhập lựa chọn (1-3): ").strip()
    
    if choice == "1":
        fix_current_project()
    elif choice == "2":
        create_smaller_setup()
    elif choice == "3":
        check_project_status("sa-drive-01-443518-jjet")
    else:
        print("❌ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
