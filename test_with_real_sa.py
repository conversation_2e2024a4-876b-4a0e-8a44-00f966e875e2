#!/usr/bin/env python3
"""
Test SA Drive với service accounts thật
Bypass bug bằng cách tạo service accounts thật từ Google Cloud
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    print("🧪 Test SA Drive với Service Accounts thật")
    print("=" * 50)
    
    # Kiểm tra đăng nhập Google Cloud
    try:
        result = subprocess.run(['gcloud', 'auth', 'list', '--filter=status:ACTIVE'], 
                              capture_output=True, text=True, check=True)
        if not result.stdout.strip():
            print("❌ Chưa đăng nhập Google Cloud")
            print("📋 Chạy: gcloud auth login")
            return
        else:
            print("✅ Đã đăng nhập Google Cloud")
    except subprocess.CalledProcessError:
        print("❌ Lỗi kiểm tra Google Cloud auth")
        return
    
    # Tạo một project test
    project_id = f"sa-drive-test-{int(__import__('time').time())}"
    print(f"\n🏗️  Tạo test project: {project_id}")
    
    try:
        # Tạo project
        subprocess.run([
            'gcloud', 'projects', 'create', project_id,
            '--name', 'SA Drive Test Project'
        ], check=True, capture_output=True)
        
        print(f"✅ Đã tạo project: {project_id}")
        
        # Set project
        subprocess.run(['gcloud', 'config', 'set', 'project', project_id], 
                     check=True, capture_output=True)
        
        # Enable Drive API
        print("🔧 Enable Drive API...")
        subprocess.run([
            'gcloud', 'services', 'enable', 'drive.googleapis.com'
        ], check=True, capture_output=True)
        
        # Tạo 3 service accounts
        accounts_dir = Path("config_dir/accounts")
        accounts_dir.mkdir(parents=True, exist_ok=True)
        
        # Xóa service accounts cũ
        for old_file in accounts_dir.glob("*.json"):
            old_file.unlink()
        
        print("👥 Tạo 3 service accounts...")
        for i in range(3):
            sa_name = f"sa-{i:03d}"
            sa_email = f"{sa_name}@{project_id}.iam.gserviceaccount.com"
            
            # Tạo service account
            subprocess.run([
                'gcloud', 'iam', 'service-accounts', 'create', sa_name,
                '--display-name', f'Test Service Account {i}'
            ], check=True, capture_output=True)
            
            # Tạo key
            key_file = accounts_dir / f"{i}.json"
            subprocess.run([
                'gcloud', 'iam', 'service-accounts', 'keys', 'create',
                str(key_file),
                '--iam-account', sa_email
            ], check=True, capture_output=True)
            
            print(f"  ✅ Đã tạo SA {i}: {sa_email}")
        
        print("\n🧪 Test SA Drive...")
        
        # Test sadrive details
        result = subprocess.run(['sadrive', 'details'], 
                              capture_output=True, text=True)
        print("📊 SA Drive Details:")
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        # Test update_sas
        print("\n🔄 Update Service Accounts...")
        result = subprocess.run(['sadrive', 'update_sas'], 
                              capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        # Test details lại
        print("\n📊 SA Drive Details sau update:")
        result = subprocess.run(['sadrive', 'details'], 
                              capture_output=True, text=True)
        print(result.stdout)
        
        print(f"\n🎉 Test hoàn thành với project: {project_id}")
        print("📝 Lưu ý: Đây chỉ là test với 3 SAs (45GB)")
        print("📋 Để tạo full setup 17TiB, chạy: python3 create_real_service_accounts.py")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi: {e}")
        print("📋 Vui lòng kiểm tra Google Cloud permissions")

if __name__ == "__main__":
    main()
