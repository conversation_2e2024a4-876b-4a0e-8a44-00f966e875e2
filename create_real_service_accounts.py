#!/usr/bin/env python3
"""
Tạo Service Accounts thật cho SA Drive - 17TiB Storage
"""

import os
import json
import subprocess
import time
import random
import string
from pathlib import Path

class RealSACreator:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.accounts_dir = self.base_dir / "config_dir" / "accounts"
        self.accounts_dir.mkdir(parents=True, exist_ok=True)
        
        # Xóa service accounts mẫu cũ
        for old_file in self.accounts_dir.glob("*.json"):
            old_file.unlink()
        
        print("🚀 Tạo Service Accounts thật cho SA Drive")
        print("=" * 50)
    
    def generate_project_id(self, index):
        """Tạo project ID unique"""
        timestamp = str(int(time.time()))[-6:]
        random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=4))
        return f"sa-drive-{index:02d}-{timestamp}-{random_suffix}"
    
    def check_auth(self):
        """Kiểm tra đăng nhập Google Cloud"""
        try:
            result = subprocess.run(['gcloud', 'auth', 'list', '--filter=status:ACTIVE'], 
                                  capture_output=True, text=True, check=True)
            if result.stdout.strip():
                print("✅ Đã đăng nhập Google Cloud")
                return True
            else:
                print("❌ Chưa đăng nhập Google Cloud")
                return False
        except subprocess.CalledProcessError:
            print("❌ Lỗi kiểm tra auth")
            return False
    
    def create_single_project(self, project_index, max_sa=10):
        """Tạo một project với service accounts"""
        project_id = self.generate_project_id(project_index)
        
        print(f"\n🏗️  Tạo Project {project_index}: {project_id}")
        
        try:
            # Tạo project
            subprocess.run([
                'gcloud', 'projects', 'create', project_id,
                '--name', f'SA Drive Project {project_index}'
            ], check=True, capture_output=True, text=True)
            
            print(f"✅ Đã tạo project: {project_id}")
            
            # Set project làm default tạm thời
            subprocess.run(['gcloud', 'config', 'set', 'project', project_id], 
                         check=True, capture_output=True)
            
            # Enable Drive API
            print("🔧 Enable Drive API...")
            subprocess.run([
                'gcloud', 'services', 'enable', 'drive.googleapis.com'
            ], check=True, capture_output=True)
            
            # Tạo service accounts
            service_accounts = []
            for i in range(max_sa):
                sa_name = f"sa-{i:03d}"
                sa_email = f"{sa_name}@{project_id}.iam.gserviceaccount.com"
                
                try:
                    # Tạo service account
                    subprocess.run([
                        'gcloud', 'iam', 'service-accounts', 'create', sa_name,
                        '--display-name', f'Service Account {i}',
                        '--description', f'SA Drive Service Account {i}'
                    ], check=True, capture_output=True)
                    
                    # Tạo key file
                    key_file = self.accounts_dir / f"{project_index:02d}-{i:03d}.json"
                    subprocess.run([
                        'gcloud', 'iam', 'service-accounts', 'keys', 'create',
                        str(key_file),
                        '--iam-account', sa_email
                    ], check=True, capture_output=True)
                    
                    service_accounts.append(sa_email)
                    
                    if (i + 1) % 5 == 0:
                        print(f"  ✅ Đã tạo {i + 1}/{max_sa} SAs")
                    
                    time.sleep(1)  # Tránh rate limiting
                    
                except subprocess.CalledProcessError as e:
                    print(f"❌ Lỗi tạo SA {sa_name}: {e}")
                    continue
            
            print(f"🎉 Hoàn thành project {project_id}: {len(service_accounts)} SAs")
            return project_id, service_accounts
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Lỗi tạo project {project_id}: {e}")
            return None, []
    
    def create_test_setup(self):
        """Tạo setup test với 2 projects, mỗi project 10 SAs"""
        print("🧪 Tạo Test Setup: 2 projects × 10 SAs = 20 SAs (300GB)")
        
        if not self.check_auth():
            print("\n📋 Vui lòng đăng nhập trước:")
            print("1. Chạy: gcloud auth login")
            print("2. Chọn Gmail account của bạn")
            print("3. Chạy lại script này")
            return
        
        all_projects = []
        all_sas = []
        
        for i in range(1, 3):  # Tạo 2 projects
            project_id, sas = self.create_single_project(i, max_sa=10)
            if project_id:
                all_projects.append(project_id)
                all_sas.extend(sas)
        
        print(f"\n🎉 Test Setup hoàn thành!")
        print(f"📊 Tổng: {len(all_projects)} projects, {len(all_sas)} SAs")
        print(f"💾 Storage: {len(all_sas) * 15}GB = {len(all_sas) * 15 / 1024:.1f}TiB")
        
        # Tạo summary file
        summary = {
            "projects": all_projects,
            "service_accounts": all_sas,
            "total_storage_gb": len(all_sas) * 15,
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        with open(self.base_dir / "sa_summary.json", 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"📝 Summary saved to: sa_summary.json")
        
        return all_projects, all_sas
    
    def create_full_setup(self):
        """Tạo full setup: 12 projects × 100 SAs = 1200 SAs (17TiB)"""
        print("🚀 Tạo Full Setup: 12 projects × 100 SAs = 1200 SAs (17TiB)")
        print("⚠️  Quá trình này sẽ mất 2-3 giờ!")
        
        confirm = input("Bạn có chắc muốn tiếp tục? (y/N): ")
        if confirm.lower() != 'y':
            print("❌ Hủy bỏ")
            return
        
        if not self.check_auth():
            print("❌ Vui lòng đăng nhập Google Cloud trước")
            return
        
        all_projects = []
        all_sas = []
        
        for i in range(1, 13):  # Tạo 12 projects
            project_id, sas = self.create_single_project(i, max_sa=100)
            if project_id:
                all_projects.append(project_id)
                all_sas.extend(sas)
            
            # Nghỉ giữa các projects
            if i < 12:
                print(f"⏳ Nghỉ 30s trước project tiếp theo...")
                time.sleep(30)
        
        print(f"\n🎉 Full Setup hoàn thành!")
        print(f"📊 Tổng: {len(all_projects)} projects, {len(all_sas)} SAs")
        print(f"💾 Storage: {len(all_sas) * 15}GB = {len(all_sas) * 15 / 1024:.1f}TiB")
        
        return all_projects, all_sas

def main():
    creator = RealSACreator()
    
    print("Chọn setup:")
    print("1. Test Setup (2 projects × 10 SAs = 300GB)")
    print("2. Full Setup (12 projects × 100 SAs = 17TiB)")
    
    choice = input("Nhập lựa chọn (1/2): ").strip()
    
    if choice == "1":
        creator.create_test_setup()
    elif choice == "2":
        creator.create_full_setup()
    else:
        print("❌ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
