#!/usr/bin/env python3
"""
Fix SA Drive path bug trên macOS
Bug: SA Drive sử dụng backslash (\) thay vì forward slash (/) trên macOS
"""

import os
import sys
import shutil
from pathlib import Path

def find_sadrive_installation():
    """Tìm nơi cài đặt SA Drive"""
    try:
        import sadrive
        sadrive_path = Path(sadrive.__file__).parent
        return sadrive_path
    except ImportError:
        print("❌ SA Drive chưa được cài đặt")
        return None

def backup_original_files(sadrive_path):
    """Backup các file gốc"""
    backup_dir = sadrive_path / "backup_original"
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "helpers/dbf.py",
        "helpers/drive.py"
    ]
    
    for file_path in files_to_backup:
        src = sadrive_path / file_path
        dst = backup_dir / file_path.replace("/", "_")
        if src.exists() and not dst.exists():
            shutil.copy2(src, dst)
            print(f"✅ Backup: {file_path}")

def fix_dbf_py(sadrive_path):
    """Fix đường dẫn trong dbf.py"""
    dbf_file = sadrive_path / "helpers" / "dbf.py"
    
    if not dbf_file.exists():
        print(f"❌ Không tìm thấy: {dbf_file}")
        return False
    
    # Đọc nội dung file
    with open(dbf_file, 'r') as f:
        content = f.read()
    
    # Fix các đường dẫn sử dụng backslash
    original_content = content
    
    # Thay thế các pattern có vấn đề
    content = content.replace(
        "file_path = os.path.join(accounts_dir, f'{sa_number}.json')",
        "file_path = os.path.join(accounts_dir, f'{sa_number}.json').replace('\\\\', '/')"
    )
    
    # Thêm import os nếu chưa có
    if "import os" not in content and "from os" not in content:
        content = "import os\n" + content
    
    # Ghi lại file nếu có thay đổi
    if content != original_content:
        with open(dbf_file, 'w') as f:
            f.write(content)
        print("✅ Fixed dbf.py")
        return True
    else:
        print("ℹ️  dbf.py không cần fix")
        return False

def fix_drive_py(sadrive_path):
    """Fix đường dẫn trong drive.py"""
    drive_file = sadrive_path / "helpers" / "drive.py"
    
    if not drive_file.exists():
        print(f"❌ Không tìm thấy: {drive_file}")
        return False
    
    # Đọc nội dung file
    with open(drive_file, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Fix các đường dẫn có vấn đề
    content = content.replace(
        "keyfile_name = os.path.join(accounts_dir, f'{self.sa_number}.json')",
        "keyfile_name = os.path.join(accounts_dir, f'{self.sa_number}.json').replace('\\\\', '/')"
    )
    
    # Ghi lại file nếu có thay đổi
    if content != original_content:
        with open(drive_file, 'w') as f:
            f.write(content)
        print("✅ Fixed drive.py")
        return True
    else:
        print("ℹ️  drive.py không cần fix")
        return False

def create_path_fix_patch():
    """Tạo patch file để fix đường dẫn"""
    patch_content = '''#!/usr/bin/env python3
"""
Patch để fix SA Drive path bug trên macOS
"""

import os
import sys
from pathlib import Path

# Monkey patch os.path.join để fix backslash issue
original_join = os.path.join

def fixed_join(*args):
    result = original_join(*args)
    # Fix backslash trên macOS
    if sys.platform == "darwin":
        result = result.replace("\\\\", "/")
    return result

os.path.join = fixed_join

print("✅ SA Drive path bug đã được patch")
'''
    
    with open("sadrive_path_patch.py", 'w') as f:
        f.write(patch_content)
    
    print("📝 Đã tạo patch file: sadrive_path_patch.py")

def main():
    print("🔧 Fix SA Drive Path Bug trên macOS")
    print("=" * 40)
    
    # Tìm SA Drive installation
    sadrive_path = find_sadrive_installation()
    if not sadrive_path:
        return
    
    print(f"📍 SA Drive path: {sadrive_path}")
    
    # Backup original files
    backup_original_files(sadrive_path)
    
    # Fix các file
    fixed_files = []
    
    if fix_dbf_py(sadrive_path):
        fixed_files.append("dbf.py")
    
    if fix_drive_py(sadrive_path):
        fixed_files.append("drive.py")
    
    # Tạo patch file
    create_path_fix_patch()
    
    if fixed_files:
        print(f"\n🎉 Đã fix {len(fixed_files)} files: {', '.join(fixed_files)}")
        print("🔄 Vui lòng restart terminal và thử lại SA Drive")
    else:
        print("\nℹ️  Không có file nào cần fix")
    
    print("\n📋 Để áp dụng patch:")
    print("1. Import patch trước khi sử dụng SA Drive:")
    print("   python3 -c 'import sadrive_path_patch; import sadrive'")
    print("2. Hoặc thêm vào đầu script:")
    print("   import sadrive_path_patch")

if __name__ == "__main__":
    main()
