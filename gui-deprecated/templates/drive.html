<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.0-beta1/dist/css/bootstrap.min.css" rel="stylesheet"
        crossorigin="anonymous">
    <title>SA Drive</title>
    <link rel="stylesheet" href="./../static/css/style.css">
</head>

<body class="bg-dark text-white">
    <button id="hiddenlinksmodalbtn" style="display: none;" type="button" data-bs-toggle="modal"
        data-bs-target="#linkmodal"></button>
    <div class="modal fade" id="linkmodal" tabindex="-1" aria-labelledby="linkmodallabel" aria-hidden="true">
        <div class="modal-dialog modal-xl bg-dark text-white">
            <div class="modal-content bg-dark text-white">
                <div class="modal-header bg-dark text-white">
                    <h1 class="modal-title fs-5" id="linkmodallabel">The links of shared files:</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div id="sharedlinksplace" class="modal-body bg-dark text-white">

                </div>
                <div class="modal-footer bg-dark text-white">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <button id="hiddenuploadinfobtn" style="display: none;" type="button" data-bs-toggle="modal"
        data-bs-target="#uploadinfo"></button>
    <div class="modal fade" id="uploadinfo" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="uploadinfolabel" aria-hidden="true">
        <div class="modal-dialog modal-xl bg-dark text-white">
            <div class="modal-content bg-dark text-white">
                <div class="modal-header bg-dark text-white">
                    <h1 class="modal-title fs-5" id="uploadinfolabel">Please make sure of the following while uploading:
                    </h1>
                    <button type="button" class="btn-close" onclick="window.location.reload();" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body bg-dark text-white">
                    <ol class="list-group list-group-numbered">
                        <li class="list-group-item list-group-item-warning">You can upload only ONE folder at a time.
                        </li>
                        <li class="list-group-item list-group-item-warning">You cannot upload files and folders
                            together.</li>
                        <li class="list-group-item list-group-item-warning">You can upload multiple files together.</li>
                    </ol>
                    <br>
                    For example, if you have to upload 3 files and 2 folders, your work flow will be like:
                    <br>
                    <ol class="list-group list-group-numbered">
                        <li class="list-group-item list-group-item-info">Upload the three files together by dragging and
                            dropping them.</li>
                        <li class="list-group-item list-group-item-info">Upload the first folder by dragging and
                            dropping it.</li>
                        <li class="list-group-item list-group-item-info">Upload the second folder by dragging and
                            dropping it.</li>
                    </ol>
                    <br>
                    Note that there can be as many sub-folders and sub-files within the folder you upload.
                </div>
                <div class="modal-footer bg-dark text-white">
                    <button type="button" class="btn btn-primary" onclick="window.location.reload();"
                        data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="searchmodal" tabindex="-1" aria-labelledby="searchmodallabel" aria-hidden="true">
        <div class="modal-dialog modal-xl bg-dark text-white">
            <div class="modal-content bg-dark text-white">
                <form role="search" method="POST" action="{{ url_for('_search') }}">
                    <div class="modal-header bg-dark text-white">
                        <h1 class="modal-title fs-5" id="searchmodallabel">The links of shared files:</h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body bg-dark text-white">
                        <div class="mb-3">
                            <input class="form-control me-2" type="search" placeholder="Search" aria-label="Search"
                                name="searched">
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="fuzzycheckbox" name="fuzzy">
                            <label class="form-check-label " for="fuzzycheckbox">Fuzzy Search ?</label>
                        </div>
                        {{ form.hidden_tag() }}
                        <br>
                        if you enable fuzzy search then "alska" will match with "alaska".
                        <br>
                        if you do not enable it then the search term will be checked if it is in the file name.<br>
                        Example: If you search for "list" then it will match with "Blacklist.S01E01.1080p"
                        <br>
                        Note: <code>fuzzy search is slower, and might give wrong results, or even no results</code>

                    </div>
                    <div class="modal-footer bg-dark text-white">
                        <button class="btn btn-success" type="submit">Search</button>
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <menu id="ctxMenu">
        <ul>
            <li><button onclick="sharefrommenu(this.parentElement.parentElement.parentElement.dataset.id)"><svg
                        class="a-s-fa-Ha-pa c-qd" width="24px" height="24px" viewBox="0 0 24 24" focusable="false"
                        fill="currentColor">
                        <path
                            d="M9 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0-6c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm0 7c-2.67 0-8 1.34-8 4v3h16v-3c0-2.66-5.33-4-8-4zm6 5H3v-.99C3.2 16.29 6.3 15 9 15s5.8 1.29 6 2v1zm3-4v-3h-3V9h3V6h2v3h3v2h-3v3h-2z">
                        </path>
                    </svg>
                    &nbsp; Share
                </button>
            </li>
            <li><button onclick="unsharethis(this.parentElement.parentElement.parentElement.dataset.id)"><svg
                        class="a-s-fa-Ha-pa c-qd" width="24px" height="24px" viewBox="0 0 24 24" focusable="false"
                        fill="currentColor">
                        <path
                            d="M 9 12 c 2.21 0 4 -1.79 4 -4 s -1.79 -4 -4 -4 s -4 1.79 -4 4 s 1.79 4 4 4 z m 0 -6 c 1.1 0 2 0.9 2 2 s -0.9 2 -2 2 s -2 -0.9 -2 -2 s 0.9 -2 2 -2 z m 0 7 c -2.67 0 -8 1.34 -8 4 v 3 h 16 v -3 c 0 -2.66 -5.33 -4 -8 -4 z m 6 5 H 3 v -0.99 C 3.2 16.29 6.3 15 9 15 s 5.8 1.29 6 2 v 1 z m 3 -7 h -3 V 9 h 3 V 9 h 2 v 0 h 3 v 2 h -3 v 0 h -2 z">
                        </path>
                    </svg>
                    &nbsp; Unshare
                </button>
            </li>
            <li><button onclick="renamethis(this.parentElement.parentElement.parentElement.dataset.id)"><svg
                        class="a-s-fa-Ha-pa c-qd" width="24px" height="24px" viewBox="0 0 24 24" focusable="false"
                        fill="currentColor">
                        <path d="M0 0h24v24H0z" fill="none"></path>
                        <path
                            d="M18.41 5.8L17.2 4.59c-.78-.78-2.05-.78-2.83 0l-2.68 2.68L3 15.96V20h4.04l8.74-8.74 2.63-2.63c.79-.78.79-2.05 0-2.83zM6.21 18H5v-1.21l8.66-8.66 1.21 1.21L6.21 18zM11 20l4-4h6v4H11z">
                        </path>
                    </svg>
                    &nbsp; Rename
                </button>
            </li>
            <li><button onclick="downloadthis(this.parentElement.parentElement.parentElement.dataset.id);"><svg
                        class="a-s-fa-Ha-pa c-qd" width="24px" height="24px" viewBox="0 0 24 24" focusable="false"
                        fill="currentColor">
                        <path
                            d="M4 15h2v3h12v-3h2v3c0 1.1-.9 2-2 2H6c-1.1 0-2-.9-2-2m11.59-8.41L13 12.17V4h-2v8.17L8.41 9.59 7 11l5 5 5-5-1.41-1.41z">
                        </path>
                    </svg>
                    &nbsp; Download
                </button>
            </li>
            <li><button onclick="deletefrommenu(this.parentElement.parentElement.parentElement.dataset.id)"><svg
                        viewBox="0 0 24 24" fill="currentColor" focusable="false" class=" c-qd a-s-fa-Ha-pa">
                        <path d="M0 0h24v24H0V0z" fill="none"></path>
                        <path
                            d="M15 4V3H9v1H4v2h1v13c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V6h1V4h-5zm2 15H7V6h10v13zM9 8h2v9H9zm4 0h2v9h-2z">
                        </path>
                    </svg>
                    &nbsp; Delete
                </button>
            </li>
            <li><button onclick="starfrommenu(this.parentElement.parentElement.parentElement.dataset.id)"><svg
                        class="a-s-fa-Ha-pa c-qd" width="24px" height="24px" viewBox="0 0 24 24" focusable="false"
                        fill="currentColor">
                        <path
                            d="M22 9.24l-7.19-.62L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.63-7.03L22 9.24zM12 15.4l-3.76 2.27 1-4.28-3.32-2.88 4.38-.38L12 6.1l1.71 4.04 4.38.38-3.32 2.88 1 4.28L12 15.4z">
                        </path>
                    </svg>
                    &nbsp; Star
                </button>
            </li>
            <li><button onclick="unstarfrommenu(this.parentElement.parentElement.parentElement.dataset.id)"><svg
                        class="a-s-fa-Ha-pa c-qd" width="24px" height="24px" viewBox="0 0 24 24" focusable="false"
                        fill="currentColor">
                        <path
                            d="M22 9.24l-7.19-.62L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.63-7.03L22 9.24zM12 15.4l-3.76 2.27 1-4.28-3.32-2.88 4.38-.38L12 6.1l1.71 4.04 4.38.38-3.32 2.88 1 4.28L12 15.4z">
                        </path>
                    </svg>
                    &nbsp; Unstar
                </button>
            </li>
        </ul>
    </menu>
    <nav class="navbar navbar-expand-lg bg-body-tertiary " style="background-color: #02233b;">
        <div class="container-fluid">
            <div style="display: inherit;">
                <a class="navbar-brand text-white" href="/drive">
                    <img src="/static/images/icon.png" alt="Logo" width="35" height="35"
                        class="d-inline-block align-text-top">
                    SA Drive
                </a>
                <ul class="nav justify-content-start">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            Additional Options
                        </a>
                        <ul class="dropdown-menu">
                            <li>
                                <a href="https://drive.google.com/drive/u/0/folders/{{ this_id }}" class="dropdown-item"
                                    target="_blank" rel="noopener noreferrer">View in google drive</a>
                            </li>
                            <li>
                                <a href="/starred" class="dropdown-item" target="_blank" rel="noopener noreferrer">Show
                                    starred files</a>
                            </li>
                            <li>
                                <a href="/sasdetails" class="dropdown-item" target="_blank"
                                    rel="noopener noreferrer">Show
                                    SA details</a>
                            </li>
                            <!-- <li><a class="dropdown-item" href="#">Another action</a></li>
                      <li><hr class="dropdown-divider"></li>
                      <li><a class="dropdown-item" href="#">Something else here</a></li> -->
                        </ul>
                    </li>
                </ul>
            </div>



            <ul class="nav justify-content-end">
                <li class="nav-item">
                    <button class="btn btn-outline-success text-white me-2" type="button" data-bs-toggle="modal"
                        data-bs-target="#searchmodal">Search Files
                        <svg focusable="false" height="24px" viewBox="0 0 24 24" width="24px"
                            xmlns="http://www.w3.org/2000/svg" fill="currentColor">
                            <path
                                d="M20.49,19l-5.73-5.73C15.53,12.2,16,10.91,16,9.5C16,5.91,13.09,3,9.5,3S3,5.91,3,9.5C3,13.09,5.91,16,9.5,16 c1.41,0,2.7-0.47,3.77-1.24L19,20.49L20.49,19z M5,9.5C5,7.01,7.01,5,9.5,5S14,7.01,14,9.5S11.99,14,9.5,14S5,11.99,5,9.5z">
                            </path>
                        </svg></button>
                </li>
                <li class="nav-item">
                    <button class="btn btn-outline-warning text-white me-2" type="button" onclick="starSelected()">Star
                        Selected Items <svg class="a-s-fa-Ha-pa c-qd" width="24px" height="24px" viewBox="0 0 24 24"
                            focusable="false" fill="currentColor">
                            <path
                                d="M22 9.24l-7.19-.62L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.63-7.03L22 9.24zM12 15.4l-3.76 2.27 1-4.28-3.32-2.88 4.38-.38L12 6.1l1.71 4.04 4.38.38-3.32 2.88 1 4.28L12 15.4z">
                            </path>
                        </svg>
                    </button>
                </li>
                <li class="nav-item">
                    <button class="btn btn-outline-warning text-white me-2" type="button"
                        onclick="unstarSelected()">Unstar
                        Selected Items <svg class="a-s-fa-Ha-pa c-qd" width="24px" height="24px" viewBox="0 0 24 24"
                            focusable="false" fill="currentColor">
                            <path
                                d="M22 9.24l-7.19-.62L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.63-7.03L22 9.24zM12 15.4l-3.76 2.27 1-4.28-3.32-2.88 4.38-.38L12 6.1l1.71 4.04 4.38.38-3.32 2.88 1 4.28L12 15.4z">
                            </path>
                        </svg>
                    </button>
                </li>
                <li class="nav-item">
                    <button class="btn btn-outline-danger text-white me-2" type="button"
                        onclick="deleteSelected()">Delete Selected Items <svg class="c-qd" viewBox="0 0 24 24"
                            fill="currentColor" focusable="false" class=" c-qd a-s-fa-Ha-pa">
                            <path d="M0 0h24v24H0V0z" fill="none"></path>
                            <path
                                d="M15 4V3H9v1H4v2h1v13c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V6h1V4h-5zm2 15H7V6h10v13zM9 8h2v9H9zm4 0h2v9h-2z">
                            </path>
                        </svg>
                    </button>
                </li>
                <li class="nav-item">
                    <button class="btn btn-outline-success text-white me-2" type="button"
                        onclick="createFolder()">Create
                        Folder
                        Here</button>
                </li>
                <li class="nav-item">
                    <button class="btn btn-outline-success text-white me-2" type="button" data-bs-toggle="modal"
                        data-bs-target="#exampleModal">Upload
                        Files
                        Here</button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="modal fade " id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl bg-dark text-white">
            <div class="modal-content bg-dark text-white">
                <div class="modal-header bg-dark text-white">
                    <h1 class="modal-title fs-5" id="exampleModalLabel">Drag and drop your files here</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div id="drop_area" class="modal-body bg-dark text-white">
                    <div style="padding:50px;">
                        Drag and drop your files here to upload.
                        <br>
                        
                    </div>
                    <br>
                    <div>Uploading ....<br>You will be notified when upload is complete</div>
                    <br>
                    <div id="progress_div"></div>
                </div>
                <div class="modal-footer bg-dark text-white">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
                    <button type="button" disabled id='upload_progress' class="btn btn-warning">Processing
                        Progress</button>
                    <button type="button" disabled id="speed" class="btn btn-warning">Processing Speed</button>
                </div>
            </div>
        </div>
    </div>

    <div class="container-md">
        <br>
        <h4 class="h4 text-center">/ <a href="/drive"
                class="link-light link-offset-2 link-underline-opacity-25 link-underline-opacity-100-hover">drive</a> /
        </h4>
        <br>
        <br>
        <table class="table text-white">
            <thead>
                <tr>
                    <th scope="col">Checkbox</th>
                    <th scope="col">File Name</th>
                    <th scope="col" class="text-end">Flie Size</th>
                    <th scope="col" class="text-end">SA index</th>
                    <th scope="col" class="text-end">Drive Link</th>
                </tr>
            </thead>
            <tbody>
                {% for child in items %}

                <tr data-id="{{ child['_id'] }}" oncontextmenu="rightclickctxmenu(event,this.dataset.id)">
                    <td>
                        <input class="form-check-input inp" type="checkbox" id="{{ child['_id'] }}">
                        <label class="form-check-label" for="{{ child['_id'] }}"></label>
                    </td>
                    {% if child['type'] == 'folder' %}
                    <td>
                        <img src="/static/images/folder.png" alt="Logo" width="22" height="22">
                        <a href="/viewfolder/{{ child['_id'] }}" class="list-group-item" style="display: inline;">
                            {% if child in starred_items %}
                            ⭐&nbsp;
                            {% endif %}
                            {{ child['file_name'] }}/</a>
                        {% if child['shared'] %}
                        &nbsp;
                        <svg width="20px" fill="currentColor" height="20px" viewBox="0 0 16 16" focusable="false"
                            class=" HHjeWe c-qd a-s-fa-Ha-pa" fill="">
                            <path
                                d="M5,7 C6.11,7 7,6.1 7,5 C7,3.9 6.11,3 5,3 C3.9,3 3,3.9 3,5 C3,6.1 3.9,7 5,7 L5,7 Z M11,7 C12.11,7 13,6.1 13,5 C13,3.9 12.11,3 11,3 C9.89,3 9,3.9 9,5 C9,6.1 9.9,7 11,7 L11,7 Z M5,8.2 C3.33,8.2 0,9.03 0,10.7 L0,12 L10,12 L10,10.7 C10,9.03 6.67,8.2 5,8.2 L5,8.2 Z M11,8.2 C10.75,8.2 10.46,8.22 10.16,8.26 C10.95,8.86 11.5,9.66 11.5,10.7 L11.5,12 L16,12 L16,10.7 C16,9.03 12.67,8.2 11,8.2 L11,8.2 Z">
                            </path>
                        </svg>
                        {% endif %}
                    </td>
                    {% else %}
                    <td>
                        <img src="/static/images/file.png" alt="Logo" width="22" height="22">
                        <a href="/viewfile/{{ child['_id'] }}" target="_blank" rel="noopener noreferrer"
                            class="list-group-item" style="display: inline;">
                            {% if child in starred_items %}
                            ⭐&nbsp;
                            {% endif %}
                            {{ child['file_name'] }}</a>
                        {% if child['shared'] %}
                        &nbsp;
                        <svg width="20px" fill="currentColor" height="20px" viewBox="0 0 16 16" focusable="false"
                            class=" HHjeWe c-qd a-s-fa-Ha-pa" fill="">
                            <path
                                d="M5,7 C6.11,7 7,6.1 7,5 C7,3.9 6.11,3 5,3 C3.9,3 3,3.9 3,5 C3,6.1 3.9,7 5,7 L5,7 Z M11,7 C12.11,7 13,6.1 13,5 C13,3.9 12.11,3 11,3 C9.89,3 9,3.9 9,5 C9,6.1 9.9,7 11,7 L11,7 Z M5,8.2 C3.33,8.2 0,9.03 0,10.7 L0,12 L10,12 L10,10.7 C10,9.03 6.67,8.2 5,8.2 L5,8.2 Z M11,8.2 C10.75,8.2 10.46,8.22 10.16,8.26 C10.95,8.86 11.5,9.66 11.5,10.7 L11.5,12 L16,12 L16,10.7 C16,9.03 12.67,8.2 11,8.2 L11,8.2 Z">
                            </path>
                        </svg>
                        {% endif %}
                    </td>
                    {% endif %}

                    <td class="text-end">
                        {% if child['file_size'] == 0 %}
                        -
                        {% else %}
                        {{ humanbytes(child['file_size']) }}
                        {% endif %}
                    </td>
                    <td class="text-end"> {{ child['service_acc_num'] }} </td>

                    {% if child['type'] == 'folder' %}
                    <td class="text-end">
                        <a href="https://drive.google.com/drive/folders/{{ child['_id'] }}" class="list-group-item"
                            style="display: inline;" target="_blank" rel="noopener noreferrer">
                            <img src="/static/images/link.png" alt="Logo" width="22" height="22">
                        </a>
                    </td>
                    {% else %}
                    <td class="text-end">
                        <a href="https://drive.google.com/file/d/{{ child['_id'] }}/view" class="list-group-item"
                            style="display: inline;" target="_blank" rel="noopener noreferrer">
                            <img src="/static/images/link.png" alt="Logo" width="22" height="22">
                        </a>
                    </td>
                    {% endif %}

                </tr>
                {% endfor %}

            </tbody>
        </table>
    </div>
    <div style="height:2.5em;"></div>
    <div class="container text-center fixed-bottom bg-dark">
        <hr>
        <code class="">
                
            </code>
    </div>
    <div class="modal-backdrop fade show" id="upload-dialog-drag" style="display: none;">
        <div class="upload-dialog text-center h1">
            <br>
            <br>
            <br>
            <br>
            <img src="./../static/images/upload.png"  class="rounded" height="256px;" alt="">
            <br><br>
            Drag and Drop<br>your files
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"
        crossorigin="anonymous"></script>
    <script src="./../static/js/script.js"></script>
    <script>
    </script>
</body>

</html>