Search.setIndex({"alltitles":{"API Reference":[[0,null]],"Command-Line Interface":[[2,null]],"Contents:":[[1,null]],"Examples":[[1,"examples"]],"How It Works":[[1,"how-it-works"]],"Indices and tables":[[1,"indices-and-tables"]],"Key Benefits":[[1,"key-benefits"]],"SA Drive (Service Account Drive)":[[1,"sa-drive-service-account-drive"]],"Sadrive Documentation":[[1,null]],"Submodules":[[4,"submodules"],[12,"submodules"]],"Why is it needed":[[1,"why-is-it-needed"]],"clearall":[[2,"sadrive-clearall"]],"config":[[2,"sadrive-config"]],"delete":[[2,"sadrive-delete"]],"details":[[2,"sadrive-details"]],"download":[[2,"sadrive-download"]],"mount":[[2,"sadrive-mount"]],"navigate":[[2,"sadrive-navigate"]],"newfolder":[[2,"sadrive-newfolder"]],"open-link":[[2,"sadrive-open-link"]],"rebuild":[[2,"sadrive-rebuild"]],"rename":[[2,"sadrive-rename"]],"sadrive":[[2,"sadrive"]],"sadrive.cli module":[[3,null]],"sadrive.commands package":[[4,null]],"sadrive.commands.config module":[[5,null]],"sadrive.commands.db module":[[6,null]],"sadrive.commands.delete module":[[7,null]],"sadrive.commands.downlaod module":[[8,null]],"sadrive.commands.manipulation module":[[9,null]],"sadrive.commands.navigate module":[[10,null]],"sadrive.commands.upload module":[[11,null]],"sadrive.helpers package":[[12,null]],"sadrive.helpers.dbf module":[[13,null]],"sadrive.helpers.drive module":[[14,null]],"sadrive.helpers.utils module":[[15,null]],"search":[[2,"sadrive-search"]],"set-dir":[[2,"sadrive-config-set-dir"]],"share":[[2,"sadrive-share"]],"show":[[2,"sadrive-config-show"]],"update-sas":[[2,"sadrive-update-sas"]],"upload":[[2,"sadrive-upload"]]},"docnames":["apireference","index","interface","modules/sadrive.cli","modules/sadrive.commands","modules/sadrive.commands.config","modules/sadrive.commands.db","modules/sadrive.commands.delete","modules/sadrive.commands.downlaod","modules/sadrive.commands.manipulation","modules/sadrive.commands.navigate","modules/sadrive.commands.upload","modules/sadrive.helpers","modules/sadrive.helpers.dbf","modules/sadrive.helpers.drive","modules/sadrive.helpers.utils"],"envversion":{"sphinx":65,"sphinx.domains.c":3,"sphinx.domains.changeset":1,"sphinx.domains.citation":1,"sphinx.domains.cpp":9,"sphinx.domains.index":1,"sphinx.domains.javascript":3,"sphinx.domains.math":2,"sphinx.domains.python":4,"sphinx.domains.rst":2,"sphinx.domains.std":2,"sphinx.ext.viewcode":1},"filenames":["apireference.rst","index.rst","interface.rst","modules\\sadrive.cli.rst","modules\\sadrive.commands.rst","modules\\sadrive.commands.config.rst","modules\\sadrive.commands.db.rst","modules\\sadrive.commands.delete.rst","modules\\sadrive.commands.downlaod.rst","modules\\sadrive.commands.manipulation.rst","modules\\sadrive.commands.navigate.rst","modules\\sadrive.commands.upload.rst","modules\\sadrive.helpers.rst","modules\\sadrive.helpers.dbf.rst","modules\\sadrive.helpers.drive.rst","modules\\sadrive.helpers.utils.rst"],"indexentries":{"--transfers":[[2,"cmdoption-sadrive-download-t",false]],"-t":[[2,"cmdoption-sadrive-download-t",false]],"_return (sadrive.commands.upload.uploadthread attribute)":[[11,"sadrive.commands.upload.UploadThread._return",false]],"add_size() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.add_size",false]],"auth (sadrive.helpers.drive.patcheddrive attribute)":[[14,"id0",false],[14,"sadrive.helpers.drive.PatchedDrive.auth",false]],"authorise() (sadrive.helpers.drive.sadrive method)":[[14,"sadrive.helpers.drive.SADrive.authorise",false]],"clear_file_map() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.clear_file_map",false]],"create_folder() (sadrive.helpers.drive.sadrive method)":[[14,"sadrive.helpers.drive.SADrive.create_folder",false]],"del_file() (in module sadrive.commands.delete)":[[7,"sadrive.commands.delete.del_file",false]],"del_folder() (in module sadrive.commands.delete)":[[7,"sadrive.commands.delete.del_folder",false]],"delete_all_files() (sadrive.helpers.drive.sadrive method)":[[14,"sadrive.helpers.drive.SADrive.delete_all_files",false]],"delete_file() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.delete_file",false]],"delete_file() (sadrive.helpers.drive.sadrive method)":[[14,"sadrive.helpers.drive.SADrive.delete_file",false]],"dest":[[2,"cmdoption-sadrive-download-arg-DEST",false]],"destination":[[2,"cmdoption-sadrive-newfolder-arg-DESTINATION",false],[2,"cmdoption-sadrive-upload-arg-DESTINATION",false]],"ff (class in sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.FF",false]],"file_id (sadrive.helpers.utils.ff attribute)":[[15,"sadrive.helpers.utils.FF.file_id",false]],"fileid":[[2,"cmdoption-sadrive-delete-arg-FILEID",false]],"filename (sadrive.helpers.utils.partinfo attribute)":[[15,"id2",false],[15,"sadrive.helpers.utils.PartInfo.filename",false]],"find_children() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.find_children",false]],"folder_exists() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.folder_exists",false]],"folderid":[[2,"cmdoption-sadrive-navigate-arg-FOLDERID",false]],"fuzzy":[[2,"cmdoption-sadrive-search-arg-FUZZY",false]],"gen (sadrive.helpers.utils.generator attribute)":[[15,"sadrive.helpers.utils.Generator.gen",false]],"generator (class in sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.Generator",false]],"get_accounts_path() (in module sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.get_accounts_path",false]],"get_config_dir() (in module sadrive.cli)":[[3,"sadrive.cli.get_config_dir",false]],"get_config_dir() (in module sadrive.commands.config)":[[5,"sadrive.commands.config.get_config_dir",false]],"get_config_dir() (in module sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.get_config_dir",false]],"get_connection() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.get_connection",false]],"get_database_path() (in module sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.get_database_path",false]],"get_dir_structure() (in module sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.get_dir_structure",false]],"get_file_details() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.get_file_details",false]],"get_file_size() (in module sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.get_file_size",false]],"get_free_sa() (in module sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.get_free_sa",false]],"get_gclone_exe() (in module sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.get_gclone_exe",false]],"get_parent_id() (in module sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.get_parent_id",false]],"get_sa_num() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.get_sa_num",false]],"get_sa_size_taken() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.get_sa_size_taken",false]],"get_size_map() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.get_size_map",false]],"humanbytes() (in module sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.humanbytes",false]],"humantime() (in module sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.humantime",false]],"id":[[2,"cmdoption-sadrive-download-arg-ID",false],[2,"cmdoption-sadrive-open-link-arg-ID",false],[2,"cmdoption-sadrive-rename-arg-ID",false],[2,"cmdoption-sadrive-share-arg-ID",false]],"insert_file() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.insert_file",false]],"insert_size_map() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.insert_size_map",false]],"join() (sadrive.commands.upload.uploadthread method)":[[11,"id0",false],[11,"sadrive.commands.upload.UploadThread.join",false]],"list_files() (sadrive.helpers.drive.sadrive method)":[[14,"sadrive.helpers.drive.SADrive.list_files",false]],"list_into_n_parts() (in module sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.list_into_n_parts",false]],"manifest (class in sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.Manifest",false]],"module":[[3,"module-sadrive.cli",false],[4,"module-sadrive.commands",false],[5,"module-sadrive.commands.config",false],[6,"module-sadrive.commands.db",false],[7,"module-sadrive.commands.delete",false],[8,"module-sadrive.commands.downlaod",false],[9,"module-sadrive.commands.manipulation",false],[10,"module-sadrive.commands.navigate",false],[11,"module-sadrive.commands.upload",false],[12,"module-sadrive.helpers",false],[13,"module-sadrive.helpers.dbf",false],[14,"module-sadrive.helpers.drive",false],[15,"module-sadrive.helpers.utils",false]],"name":[[2,"cmdoption-sadrive-newfolder-arg-NAME",false],[2,"cmdoption-sadrive-search-arg-NAME",false]],"name (sadrive.helpers.utils.ff attribute)":[[15,"sadrive.helpers.utils.FF.name",false]],"navigatehelp() (in module sadrive.commands.navigate)":[[10,"sadrive.commands.navigate.navigatehelp",false]],"newname":[[2,"cmdoption-sadrive-rename-arg-NEWNAME",false]],"original_filename (sadrive.helpers.utils.manifest attribute)":[[15,"id0",false],[15,"sadrive.helpers.utils.Manifest.original_filename",false]],"parent_id (sadrive.helpers.utils.ff attribute)":[[15,"sadrive.helpers.utils.FF.parent_id",false]],"partinfo (class in sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.PartInfo",false]],"parts (sadrive.helpers.utils.manifest attribute)":[[15,"id1",false],[15,"sadrive.helpers.utils.Manifest.parts",false]],"patcheddrive (class in sadrive.helpers.drive)":[[14,"sadrive.helpers.drive.PatchedDrive",false]],"path":[[2,"cmdoption-sadrive-config-set-dir-arg-PATH",false],[2,"cmdoption-sadrive-rebuild-arg-PATH",false],[2,"cmdoption-sadrive-upload-arg-PATH",false]],"prepare_sapart_jobs() (in module sadrive.commands.upload)":[[11,"sadrive.commands.upload.prepare_sapart_jobs",false]],"reflect_structure_on_sadrive() (in module sadrive.commands.upload)":[[11,"sadrive.commands.upload.reflect_structure_on_sadrive",false]],"remove_size() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.remove_size",false]],"rename() (sadrive.helpers.drive.sadrive method)":[[14,"sadrive.helpers.drive.SADrive.rename",false]],"rename_file() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.rename_file",false]],"reset_sa_sizes() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.reset_sa_sizes",false]],"run() (sadrive.commands.upload.uploadthread method)":[[11,"id1",false],[11,"sadrive.commands.upload.UploadThread.run",false]],"sadrive (class in sadrive.helpers.drive)":[[14,"sadrive.helpers.drive.SADrive",false]],"sadrive-config-set-dir command line option":[[2,"cmdoption-sadrive-config-set-dir-arg-PATH",false]],"sadrive-delete command line option":[[2,"cmdoption-sadrive-delete-arg-FILEID",false]],"sadrive-download command line option":[[2,"cmdoption-sadrive-download-arg-DEST",false],[2,"cmdoption-sadrive-download-arg-ID",false],[2,"cmdoption-sadrive-download-t",false]],"sadrive-navigate command line option":[[2,"cmdoption-sadrive-navigate-arg-FOLDERID",false]],"sadrive-newfolder command line option":[[2,"cmdoption-sadrive-newfolder-arg-DESTINATION",false],[2,"cmdoption-sadrive-newfolder-arg-NAME",false]],"sadrive-open-link command line option":[[2,"cmdoption-sadrive-open-link-arg-ID",false]],"sadrive-rebuild command line option":[[2,"cmdoption-sadrive-rebuild-arg-PATH",false]],"sadrive-rename command line option":[[2,"cmdoption-sadrive-rename-arg-ID",false],[2,"cmdoption-sadrive-rename-arg-NEWNAME",false]],"sadrive-search command line option":[[2,"cmdoption-sadrive-search-arg-FUZZY",false],[2,"cmdoption-sadrive-search-arg-NAME",false]],"sadrive-share command line option":[[2,"cmdoption-sadrive-share-arg-ID",false]],"sadrive-upload command line option":[[2,"cmdoption-sadrive-upload-arg-DESTINATION",false],[2,"cmdoption-sadrive-upload-arg-PATH",false]],"sadrive.cli":[[3,"module-sadrive.cli",false]],"sadrive.commands":[[4,"module-sadrive.commands",false]],"sadrive.commands.config":[[5,"module-sadrive.commands.config",false]],"sadrive.commands.db":[[6,"module-sadrive.commands.db",false]],"sadrive.commands.delete":[[7,"module-sadrive.commands.delete",false]],"sadrive.commands.downlaod":[[8,"module-sadrive.commands.downlaod",false]],"sadrive.commands.manipulation":[[9,"module-sadrive.commands.manipulation",false]],"sadrive.commands.navigate":[[10,"module-sadrive.commands.navigate",false]],"sadrive.commands.upload":[[11,"module-sadrive.commands.upload",false]],"sadrive.helpers":[[12,"module-sadrive.helpers",false]],"sadrive.helpers.dbf":[[13,"module-sadrive.helpers.dbf",false]],"sadrive.helpers.drive":[[14,"module-sadrive.helpers.drive",false]],"sadrive.helpers.utils":[[15,"module-sadrive.helpers.utils",false]],"search() (sadrive.helpers.drive.sadrive method)":[[14,"sadrive.helpers.drive.SADrive.search",false]],"search_for_file() (in module sadrive.commands.manipulation)":[[9,"sadrive.commands.manipulation.search_for_file",false]],"search_for_file_contains() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.search_for_file_contains",false]],"sem_upload_wrapper() (in module sadrive.commands.upload)":[[11,"sadrive.commands.upload.sem_upload_wrapper",false]],"set_rclone_conf() (in module sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.set_rclone_conf",false]],"share() (sadrive.helpers.drive.sadrive method)":[[14,"sadrive.helpers.drive.SADrive.share",false]],"share_file() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.share_file",false]],"share_file_base() (in module sadrive.commands.manipulation)":[[9,"sadrive.commands.manipulation.share_file_base",false]],"share_folder_recursive() (in module sadrive.commands.manipulation)":[[9,"sadrive.commands.manipulation.share_folder_recursive",false]],"shorten_fn() (in module sadrive.helpers.utils)":[[15,"sadrive.helpers.utils.shorten_fn",false]],"size (sadrive.helpers.utils.partinfo attribute)":[[15,"id3",false],[15,"sadrive.helpers.utils.PartInfo.size",false]],"space_details() (in module sadrive.helpers.dbf)":[[13,"sadrive.helpers.dbf.space_details",false]],"type (sadrive.helpers.utils.ff attribute)":[[15,"sadrive.helpers.utils.FF.type",false]],"unshare() (sadrive.helpers.drive.sadrive method)":[[14,"sadrive.helpers.drive.SADrive.unshare",false]],"upload_file() (in module sadrive.commands.upload)":[[11,"sadrive.commands.upload.upload_file",false]],"upload_file() (sadrive.helpers.drive.sadrive method)":[[14,"sadrive.helpers.drive.SADrive.upload_file",false]],"uploadthread (class in sadrive.commands.upload)":[[11,"sadrive.commands.upload.UploadThread",false]],"used_space() (sadrive.helpers.drive.sadrive method)":[[14,"sadrive.helpers.drive.SADrive.used_space",false]]},"objects":{"sadrive":[[3,0,0,"-","cli"],[4,0,0,"-","commands"],[12,0,0,"-","helpers"]],"sadrive-config-set-dir":[[2,5,1,"cmdoption-sadrive-config-set-dir-arg-PATH","PATH"]],"sadrive-delete":[[2,5,1,"cmdoption-sadrive-delete-arg-FILEID","FILEID"]],"sadrive-download":[[2,5,1,"cmdoption-sadrive-download-t","--transfers"],[2,5,1,"cmdoption-sadrive-download-t","-t"],[2,5,1,"cmdoption-sadrive-download-arg-DEST","DEST"],[2,5,1,"cmdoption-sadrive-download-arg-ID","ID"]],"sadrive-navigate":[[2,5,1,"cmdoption-sadrive-navigate-arg-FOLDERID","FOLDERID"]],"sadrive-newfolder":[[2,5,1,"cmdoption-sadrive-newfolder-arg-DESTINATION","DESTINATION"],[2,5,1,"cmdoption-sadrive-newfolder-arg-NAME","NAME"]],"sadrive-open-link":[[2,5,1,"cmdoption-sadrive-open-link-arg-ID","ID"]],"sadrive-rebuild":[[2,5,1,"cmdoption-sadrive-rebuild-arg-PATH","PATH"]],"sadrive-rename":[[2,5,1,"cmdoption-sadrive-rename-arg-ID","ID"],[2,5,1,"cmdoption-sadrive-rename-arg-NEWNAME","NEWNAME"]],"sadrive-search":[[2,5,1,"cmdoption-sadrive-search-arg-FUZZY","FUZZY"],[2,5,1,"cmdoption-sadrive-search-arg-NAME","NAME"]],"sadrive-share":[[2,5,1,"cmdoption-sadrive-share-arg-ID","ID"]],"sadrive-upload":[[2,5,1,"cmdoption-sadrive-upload-arg-DESTINATION","DESTINATION"],[2,5,1,"cmdoption-sadrive-upload-arg-PATH","PATH"]],"sadrive.cli":[[3,1,1,"","get_config_dir"]],"sadrive.commands":[[5,0,0,"-","config"],[6,0,0,"-","db"],[7,0,0,"-","delete"],[8,0,0,"-","downlaod"],[9,0,0,"-","manipulation"],[10,0,0,"-","navigate"],[11,0,0,"-","upload"]],"sadrive.commands.config":[[5,1,1,"","get_config_dir"]],"sadrive.commands.delete":[[7,1,1,"","del_file"],[7,1,1,"","del_folder"]],"sadrive.commands.manipulation":[[9,1,1,"","search_for_file"],[9,1,1,"","share_file_base"],[9,1,1,"","share_folder_recursive"]],"sadrive.commands.navigate":[[10,1,1,"","navigatehelp"]],"sadrive.commands.upload":[[11,2,1,"","UploadThread"],[11,1,1,"","prepare_sapart_jobs"],[11,1,1,"","reflect_structure_on_sadrive"],[11,1,1,"","sem_upload_wrapper"],[11,1,1,"","upload_file"]],"sadrive.commands.upload.UploadThread":[[11,3,1,"","_return"],[11,4,1,"id0","join"],[11,4,1,"id1","run"]],"sadrive.helpers":[[13,0,0,"-","dbf"],[14,0,0,"-","drive"],[15,0,0,"-","utils"]],"sadrive.helpers.dbf":[[13,1,1,"","add_size"],[13,1,1,"","clear_file_map"],[13,1,1,"","delete_file"],[13,1,1,"","find_children"],[13,1,1,"","folder_exists"],[13,1,1,"","get_connection"],[13,1,1,"","get_file_details"],[13,1,1,"","get_sa_num"],[13,1,1,"","get_sa_size_taken"],[13,1,1,"","get_size_map"],[13,1,1,"","insert_file"],[13,1,1,"","insert_size_map"],[13,1,1,"","remove_size"],[13,1,1,"","rename_file"],[13,1,1,"","reset_sa_sizes"],[13,1,1,"","search_for_file_contains"],[13,1,1,"","share_file"],[13,1,1,"","space_details"]],"sadrive.helpers.drive":[[14,2,1,"","PatchedDrive"],[14,2,1,"","SADrive"]],"sadrive.helpers.drive.PatchedDrive":[[14,3,1,"id0","auth"]],"sadrive.helpers.drive.SADrive":[[14,4,1,"","authorise"],[14,4,1,"","create_folder"],[14,4,1,"","delete_all_files"],[14,4,1,"","delete_file"],[14,4,1,"","list_files"],[14,4,1,"","rename"],[14,4,1,"","search"],[14,4,1,"","share"],[14,4,1,"","unshare"],[14,4,1,"","upload_file"],[14,4,1,"","used_space"]],"sadrive.helpers.utils":[[15,2,1,"","FF"],[15,2,1,"","Generator"],[15,2,1,"","Manifest"],[15,2,1,"","PartInfo"],[15,1,1,"","get_accounts_path"],[15,1,1,"","get_config_dir"],[15,1,1,"","get_database_path"],[15,1,1,"","get_dir_structure"],[15,1,1,"","get_file_size"],[15,1,1,"","get_free_sa"],[15,1,1,"","get_gclone_exe"],[15,1,1,"","get_parent_id"],[15,1,1,"","humanbytes"],[15,1,1,"","humantime"],[15,1,1,"","list_into_n_parts"],[15,1,1,"","set_rclone_conf"],[15,1,1,"","shorten_fn"]],"sadrive.helpers.utils.FF":[[15,3,1,"","file_id"],[15,3,1,"","name"],[15,3,1,"","parent_id"],[15,3,1,"","type"]],"sadrive.helpers.utils.Generator":[[15,3,1,"","gen"]],"sadrive.helpers.utils.Manifest":[[15,3,1,"id0","original_filename"],[15,3,1,"id1","parts"]],"sadrive.helpers.utils.PartInfo":[[15,3,1,"id2","filename"],[15,3,1,"id3","size"]]},"objnames":{"0":["py","module","Python module"],"1":["py","function","Python function"],"2":["py","class","Python class"],"3":["py","attribute","Python attribute"],"4":["py","method","Python method"],"5":["std","cmdoption","program option"]},"objtypes":{"0":"py:module","1":"py:function","2":"py:class","3":"py:attribute","4":"py:method","5":"std:cmdoption"},"terms":{"":[1,2,10,11,13,14,15],"0":13,"1":15,"100":1,"1000":14,"14":1,"15":1,"2025":1,"234":15,"7":1,"75":15,"8":2,"A":15,"By":1,"For":[2,11],"If":[1,2,9,13],"It":15,"On":3,"The":[2,9,11,14,15],"Then":2,"Will":2,"_id":[13,15],"_return":[4,11],"about":15,"access":1,"accommod":15,"accordingli":[6,7],"account":[2,3,6,7,9,11,13,14,15],"accumul":[10,11],"across":[2,3,7,11,13],"actual":[2,6,11],"ad":[6,13],"add":2,"add_siz":[2,12,13],"after":[1,11],"against":9,"aggreg":[1,13],"all":[2,3,7,9,13,14],"allow":[1,15],"alternatelink":14,"an":[10,11,14,15],"ani":[2,6,11,15],"anyon":[2,9,14],"api":[1,2,6,9,11,14],"applic":[2,3,13,15],"approxim":15,"april":1,"ar":[1,3,6],"arg":[2,11],"argument":[2,11],"around":1,"assign":[2,13,14],"attribut":14,"auth":[12,14],"authent":[11,14],"authoris":[12,14],"automat":[1,11],"avail":[11,13],"back":[1,10],"backend":14,"bar":[1,11],"base":[2,11,14,15],"been":[2,3],"befor":15,"behavior":[2,3,9,11],"binari":14,"bool":[9,13],"brows":3,"browser":[2,3,9],"buffer":15,"build":15,"built":1,"bulk":14,"byte":[11,13,14,15],"c":2,"cach":2,"call":[2,6,11],"can":15,"cap":1,"capac":[2,11,13,15],"captur":11,"chang":13,"check":[13,14],"children":10,"choos":11,"chosen":11,"chunk":1,"class":[11,14,15],"cleanup":7,"clear":2,"clear_file_map":[12,13],"clearal":[3,7],"cli":[0,1,2,6,7,8,9,11,13,15],"click":[2,5,11],"client":[13,14],"cloud":3,"codebas":1,"command":[0,1,3],"common":[9,14],"complet":[2,11,14],"compon":11,"concaten":2,"concurr":[2,6,11],"conf":15,"config":[0,1,3,4,14,15],"config_point":[2,15],"configur":[2,3,5,13,15],"confirm":2,"connect":13,"consol":2,"constant":[14,15],"construct":[2,15],"contain":[2,9,11,13,14,15],"content":[2,3,7,11,15],"context":2,"continu":10,"control":11,"convert":15,"copi":[2,8],"correspond":[2,11],"creat":[1,2,3,5,9,11,14,15],"create_fold":[12,14],"creation":[11,13,14],"credenti":6,"criteria":9,"crud":13,"ctrl":2,"ctx":2,"current":[2,5,10,11],"data":14,"databas":[2,6,7,9,13,15],"db":[0,1,2,3,4,7,9,15],"dbf":[0,1,2,12],"default":[2,13,14,15],"defin":[6,8],"del_fil":[4,7],"del_fold":[4,7],"deleg":3,"delet":[0,1,3,4,13,14],"delete_all_fil":[12,14],"delete_fil":[12,13,14],"describ":[11,15],"dest":[2,8],"destin":[1,2,11,14],"destination_folder_id":1,"detail":[3,9,11],"detect":[1,2],"determin":2,"dict":[9,11,13,14,15],"dictionari":[11,15],"dir":[1,3],"directli":9,"directori":[2,3,5,6,11,15],"dirtre":[11,15],"displai":[2,5,9,10,15],"divid":11,"doe":[2,11],"downlaod":[0,1,4],"download":[1,3,8],"drive":[0,2,3,6,7,9,10,11,12,13,15],"drive_folder_id":11,"driven":14,"durat":15,"dure":2,"e":15,"each":[1,2,3,6,11,13,15],"echo":2,"effect":2,"either":9,"ellipsi":15,"els":[2,9,13,15],"email":13,"empti":11,"enabl":15,"end":15,"enough":[1,15],"ensur":[6,11],"entri":[11,13,15],"entrypoint":3,"environ":3,"equal":15,"error":[2,11],"eta":1,"etc":3,"even":11,"everi":[2,3],"exactli":13,"exampl":3,"exce":11,"except":3,"execut":[2,11,15],"exist":[1,2,13,14],"exit":[2,10],"expect":2,"explicit":14,"explor":2,"fals":13,"familiar":1,"fetch":[2,6],"ff":[12,15],"field":[2,13],"file":[1,2,3,7,8,9,11,13,14,15],"file_id":[7,9,12,13,14,15],"file_map":[2,13],"file_nam":[9,13],"file_path":[11,15],"file_s":[13,15],"fileid":[2,7,14],"filenam":[2,9,12,13,14,15],"filenotfounderror":2,"files":11,"filesystem":[1,2,11],"filter":9,"final":2,"find":[2,13],"find_children":[12,13],"finish":11,"first":15,"flag":2,"float":15,"folder":[2,3,7,8,9,10,11,13,14,15],"folder_exist":[12,13],"folder_id":1,"folder_nam":11,"folderid":[1,2],"format":15,"found":13,"fp":11,"free":[1,2,15],"friendli":5,"from":[2,3,7,8,11,13,14,15],"full":[1,7],"function":[7,10,11,13,15],"further":15,"fuzzi":[1,2,3,9],"g":15,"gclone":[1,2,3,8,10,15],"gen":[12,15],"gener":[11,12,15],"get_accounts_path":[12,15],"get_config_dir":[3,4,5,12,15],"get_connect":[12,13],"get_database_path":[12,15],"get_dir_structur":[12,15],"get_file_detail":[12,13],"get_file_s":[12,15],"get_free_sa":[12,15],"get_gclone_ex":[12,15],"get_parent_id":[12,15],"get_sa_num":[12,13],"get_sa_size_taken":[12,13],"get_size_map":[12,13],"gib":1,"given":[1,2,13,14],"global":11,"go":10,"googl":[1,2,3,7,14,15],"googleapicli":14,"googleauth":14,"googledr":14,"googledrivefil":14,"grant":[9,14],"group":[2,3,11],"ha":[1,2,3,13],"handl":[10,11,15],"helper":[0,1,10],"hhmmss":15,"hierarchi":[2,10],"hint":1,"human":15,"humanbyt":[12,15],"humantim":[12,15],"i":[2,11,13],"id":[1,2,7,8,9,10,11,13,14,15],"identifi":[11,13,15],"includ":[2,11,14],"index":1,"inform":15,"initi":[3,13,15],"insert":[2,13],"insert_fil":[12,13],"insert_size_map":[2,12,13],"insid":9,"instanc":[11,14],"int":[1,11,13,15],"integ":14,"integr":11,"interact":[2,9,10],"interfac":[1,3,5],"interrupt":2,"invoc":3,"invok":[2,11],"iobas":14,"item":[2,9,14,15],"iter":[11,14],"its":[2,7,11,13,15],"itself":7,"job":11,"join":[4,11],"json":[1,2,6,14,15],"juggl":1,"keep":2,"kei":[11,15],"kwarg":11,"larg":[1,11],"larger":2,"launch":2,"length":15,"letter":2,"level":[2,11],"like":[2,9,14],"limit":1,"line":[1,3],"link":[3,9],"list":[9,10,11,13,14,15],"list_fil":[12,14],"list_into_n_part":[12,15],"local":[1,2,6,7,8,9,10,11],"local_s":11,"locat":2,"logic":[1,11],"longer":1,"look":13,"loop":2,"lst":15,"magic_s":15,"main":[3,11],"manag":[2,3,13],"mani":1,"manifest":[2,11,12,15],"manipul":[0,1,4],"manual":1,"map":[2,11,13,15],"mark":[9,13],"match":[9,13,14],"max":1,"max_len":15,"max_thread":15,"maximum":15,"measur":15,"media":14,"messag":2,"mib":15,"middl":15,"mirror":11,"miss":[2,3],"mmss":15,"mode":[2,6],"modul":[0,1,4,12],"modularli":3,"more":1,"mount":[1,3,10],"move":10,"movi":3,"mp4":3,"multi":[2,8],"multipl":[1,11],"n":[1,15],"name":[1,2,9,11,12,13,14,15],"navig":[0,1,3,4],"navigatehelp":[2,4,10],"need":15,"nest":[11,15],"new":[1,2,6,9,13,14],"new_file_nam":13,"new_nam":14,"newfold":[1,3,9],"newnam":[1,2],"next":15,"non":[2,9,14],"none":[10,11,13,14],"number":[2,7,9,15],"object":[13,14,15],"obtain":2,"occupi":[2,13],"occur":11,"offer":15,"one":1,"onli":1,"open":[3,9,13],"open_link":[2,9],"oper":[9,13,14,15],"option":[2,3,11],"optional_destination_id":1,"optional_folderid":1,"orchestr":11,"order":2,"origin":[2,8,15],"original_filenam":[12,15],"otherwis":13,"out":1,"overcom":1,"overs":11,"overwrit":2,"own":[2,7,14],"packag":[0,1],"page":[1,14],"parallel":[1,2],"paramet":[7,9,10,11,13,14,15],"parent":[1,2,9,11,13,14,15],"parent_folder_id":[11,14],"parent_id":[10,12,13,14,15],"parent_id_map":11,"part":[2,11,12,15],"part_file_path":11,"part_siz":11,"partinfo":[12,15],"partit":15,"patcheddr":[12,14],"path":[1,2,3,5,8,10,11,15],"per":[1,2],"percentag":14,"perform":[2,9,11,14],"perman":[2,14],"permiss":[2,9,14],"permit":15,"persist":5,"pid":11,"point":[2,11],"pointer":15,"pre":1,"prepar":11,"prepare_sapart_job":[4,11],"present":[9,10],"previou":2,"previous":2,"print":[2,14],"progress":[1,2,11,14],"project":1,"prompt":2,"provid":[1,2,7,9,10,11,13,14,15],"provis":1,"publish":14,"pydrive2":14,"queri":[9,13],"rais":2,"rclone":[3,15],"rcone":15,"read":[1,2,15],"readabl":15,"reader":[9,14],"real":[2,11],"reassembl":8,"rebuild":[3,8],"rebuilt":2,"receiv":1,"reconstruct":[3,8],"record":[2,7,9,11,13],"recurs":[2,7,9,11,15],"refer":1,"reflect":[2,11],"reflect_structure_on_sadr":[4,11],"regist":3,"releas":11,"rememb":2,"remot":[2,3,8,10,11],"remote_folder_id":11,"remov":[2,7,13,14],"remove_s":[12,13],"renam":[1,3,9,12,13,14],"rename_fil":[12,13],"report":13,"repres":[11,15],"represent":14,"requir":[2,15],"reset":[2,7,13],"reset_sa_s":[12,13],"resolv":2,"respons":[11,14],"result":[2,11],"resum":[8,14],"retriev":[9,13,14],"return":[9,10,11,13,14,15],"revok":14,"rich":[2,11],"root":[2,14,15],"rout":1,"row":13,"row_factori":13,"run":[2,4,11],"runtimeerror":2,"sa":[3,9,10,11],"sa_map":15,"sa_num":[7,9,13],"sa_num_provid":11,"sa_size_map":[2,13],"sadriv":0,"sapart":[2,3,11],"scan":[2,6],"schedul":11,"schema":13,"search":[1,3,9,12,13,14],"search_for_fil":[4,9],"search_for_file_contain":[12,13],"second":[11,15],"seek":15,"segment":[10,11],"select":[2,10,15],"sem":11,"sem_upload_wrapp":[4,11],"semaphor":11,"servic":[2,3,6,7,9,11,13,14,15],"service_acc_num":13,"service_account_id":11,"service_account_num":14,"set":[1,3,5,8,13,14],"set_dir":[2,5],"set_rclone_conf":[12,15],"share":[1,3,9,12,13,14],"share_fil":[12,13],"share_file_bas":[4,9],"share_folder_recurs":[4,9],"shareabl":[2,9],"shorten":15,"shorten_fn":[12,15],"should":11,"show":[3,5],"side":2,"signal":10,"singl":[2,7,9,11],"size":[2,6,11,12,13,15],"sort":15,"sourc":[7,9,10,11,13,14,15],"space":[1,2,11,13,14,15],"space_detail":[12,13],"specif":[3,7],"specifi":2,"split":[1,2,3,8,11,15],"sqlite":[13,15],"sqlite3":13,"start":2,"static":1,"statu":2,"stderr":2,"stdout":[2,14],"storag":[1,2,3,6,9,15],"store":[11,15],"str":[7,9,10,11,13,14,15],"stream":[2,11,14],"stream_byt":14,"strict":3,"string":[9,15],"structur":[2,3,11],"sub":11,"subclass":[11,14],"subcommand":[2,3],"subdirectori":15,"subfold":10,"subfolder_nam":14,"sublist":15,"submodul":3,"subprocess":2,"substr":[2,9,13,14],"subtract":13,"subtre":[11,15],"success":2,"support":[7,8],"sync":[2,6,13],"synchron":[2,3,6],"sz":11,"t":[2,15],"tabl":[2,9,13],"target":[1,11,15],"task":11,"task_id":11,"team":1,"term":9,"termin":[2,10],"than":2,"them":2,"thi":[1,2,5,6,11,13,15],"those":9,"thread":[2,11,15],"threadpoolexecutor":6,"threshold":15,"through":[9,10,11],"time":[2,11,15],"timeout":11,"titl":[11,14],"tmp_drive":11,"tool":[1,2],"top":2,"total":[11,13,14,15],"total_s":11,"track":[11,13],"transfer":[1,2,8],"trash":[2,14],"travers":[2,10,11],"tree":[11,15],"true":[2,9,13],"truncat":15,"tupl":11,"type":[1,11,12,13,14,15],"typeddict":15,"under":[2,13,14],"underli":15,"unifi":1,"uniqu":[13,15],"unshar":[12,13,14],"until":[2,14],"up":[1,13,14],"updat":[6,7,9,11,13,14],"update_sa":[3,6],"upload":[0,1,3,4,14],"upload_fil":[4,11,12,14],"uploadthread":[4,11],"upon":2,"url":2,"us":[2,3,5,6,8,9,10,11,13,14,15],"usag":[2,3,6,9,13,14],"used_spac":[12,14],"user":[2,5,10],"util":[0,1,12,13],"valid":3,"valu":[2,11,13,15],"veri":1,"vf":2,"via":[1,2,3,6,9,11],"video":3,"wait":11,"web":2,"where":[2,11],"whether":13,"which":[1,2],"whose":14,"wipe":[3,7],"within":15,"wrapper":[11,14,15],"write":[2,11],"yet":2,"yield":[14,15],"you":1,"your":1,"zero":[2,13]},"titles":["API Reference","Sadrive Documentation","Command-Line Interface","sadrive.cli module","sadrive.commands package","sadrive.commands.config module","sadrive.commands.db module","sadrive.commands.delete module","sadrive.commands.downlaod module","sadrive.commands.manipulation module","sadrive.commands.navigate module","sadrive.commands.upload module","sadrive.helpers package","sadrive.helpers.dbf module","sadrive.helpers.drive module","sadrive.helpers.utils module"],"titleterms":{"It":1,"account":1,"api":0,"benefit":1,"clearal":2,"cli":3,"command":[2,4,5,6,7,8,9,10,11],"config":[2,5],"content":1,"db":6,"dbf":13,"delet":[2,7],"detail":2,"dir":2,"document":1,"downlaod":8,"download":2,"drive":[1,14],"exampl":1,"helper":[12,13,14,15],"how":1,"i":1,"indic":1,"interfac":2,"kei":1,"line":2,"link":2,"manipul":9,"modul":[3,5,6,7,8,9,10,11,13,14,15],"mount":2,"navig":[2,10],"need":1,"newfold":2,"open":2,"packag":[4,12],"rebuild":2,"refer":0,"renam":2,"sa":[1,2],"sadriv":[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15],"search":2,"servic":1,"set":2,"share":2,"show":2,"submodul":[4,12],"tabl":1,"updat":2,"upload":[2,11],"util":15,"why":1,"work":1}})