
<!DOCTYPE html>


<html lang="en" data-content_root="./" >

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Sadrive Documentation &#8212; sadrive 2.0.0 documentation</title>
  
  
  
  <script data-cfasync="false">
    document.documentElement.dataset.mode = localStorage.getItem("mode") || "";
    document.documentElement.dataset.theme = localStorage.getItem("theme") || "";
  </script>
  <!--
    this give us a css class that will be invisible only if js is disabled
  -->
  <noscript>
    <style>
      .pst-js-only { display: none !important; }

    </style>
  </noscript>
  
  <!-- Loaded before other Sphinx assets -->
  <link href="_static/styles/theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />
<link href="_static/styles/pydata-sphinx-theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />

    <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=8f2a1f02" />
  
  <!-- So that users can add custom icons -->
  <script src="_static/scripts/fontawesome.js?digest=8878045cc6db502f8baf"></script>
  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf" />
<link rel="preload" as="script" href="_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf" />

    <script src="_static/documentation_options.js?v=51b770b3"></script>
    <script src="_static/doctools.js?v=9bcbadda"></script>
    <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script>DOCUMENTATION_OPTIONS.pagename = 'index';</script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Command-Line Interface" href="interface.html" />
  <meta name="viewport" content="width=device-width, initial-scale=1"/>
  <meta name="docsearch:language" content="en"/>
  <meta name="docsearch:version" content="" />
  </head>
  
  
  <body data-bs-spy="scroll" data-bs-target=".bd-toc-nav" data-offset="180" data-bs-root-margin="0px 0px -60%" data-default-mode="">

  
  
  <div id="pst-skip-link" class="skip-link d-print-none"><a href="#main-content">Skip to main content</a></div>
  
  <div id="pst-scroll-pixel-helper"></div>
  
  <button type="button" class="btn rounded-pill" id="pst-back-to-top">
    <i class="fa-solid fa-arrow-up"></i>Back to top</button>

  
  <dialog id="pst-search-dialog">
    
<form class="bd-search d-flex align-items-center"
      action="search.html"
      method="get">
  <i class="fa-solid fa-magnifying-glass"></i>
  <input type="search"
         class="form-control"
         name="q"
         placeholder="Search the docs ..."
         aria-label="Search the docs ..."
         autocomplete="off"
         autocorrect="off"
         autocapitalize="off"
         spellcheck="false"/>
  <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd>K</kbd></span>
</form>
  </dialog>

  <div class="pst-async-banner-revealer d-none">
  <aside id="bd-header-version-warning" class="d-none d-print-none" aria-label="Version warning"></aside>
</div>

  
    <header class="bd-header navbar navbar-expand-lg bd-navbar d-print-none">
<div class="bd-header__inner bd-page-width">
  <button class="pst-navbar-icon sidebar-toggle primary-toggle" aria-label="Site navigation">
    <span class="fa-solid fa-bars"></span>
  </button>
  
  
  <div class="col-lg-3 navbar-header-items__start">
    
      <div class="navbar-item">

  
    
  

<a class="navbar-brand logo" href="#">
  
  
  
  
  
  
    <p class="title logo__title">sadrive 2.0.0 documentation</p>
  
</a></div>
    
  </div>
  
  <div class="col-lg-9 navbar-header-items">
    
    <div class="me-auto navbar-header-items__center">
      
        <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item ">
  <a class="nav-link nav-internal" href="apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
      
    </div>
    
    
    <div class="navbar-header-items__end">
      
        <div class="navbar-item navbar-persistent--container">
          

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
        </div>
      
      
        <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
      
    </div>
    
  </div>
  
  
    <div class="navbar-persistent--mobile">

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
    </div>
  

  
    <button class="pst-navbar-icon sidebar-toggle secondary-toggle" aria-label="On this page">
      <span class="fa-solid fa-outdent"></span>
    </button>
  
</div>

    </header>
  

  <div class="bd-container">
    <div class="bd-container__inner bd-page-width">
      
      
      
        
      
      <dialog id="pst-primary-sidebar-modal"></dialog>
      <div id="pst-primary-sidebar" class="bd-sidebar-primary bd-sidebar hide-on-wide">
        

  
  <div class="sidebar-header-items sidebar-primary__section">
    
    
      <div class="sidebar-header-items__center">
        
          
          
            <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item ">
  <a class="nav-link nav-internal" href="apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
          
        
      </div>
    
    
    
      <div class="sidebar-header-items__end">
        
          <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
        
      </div>
    
  </div>
  
  
  <div class="sidebar-primary-items__end sidebar-primary__section">
      <div class="sidebar-primary-item">
<div id="ethical-ad-placement"
      class="flat"
      data-ea-publisher="readthedocs"
      data-ea-type="readthedocs-sidebar"
      data-ea-manual="true">
</div></div>
  </div>


      </div>
      
      <main id="main-content" class="bd-main" role="main">
        
        
          <div class="bd-content">
            <div class="bd-article-container">
              
              <div class="bd-header-article d-print-none"></div>
              
              
              
                
<div id="searchbox"></div>
                <article class="bd-article">
                  
  <section id="sadrive-documentation">
<h1>Sadrive Documentation<a class="headerlink" href="#sadrive-documentation" title="Link to this heading">#</a></h1>
<section id="sa-drive-service-account-drive">
<h2>SA Drive (Service Account Drive)<a class="headerlink" href="#sa-drive-service-account-drive" title="Link to this heading">#</a></h2>
<p>This project unifies the storage of multiple pre-April 15 2025 Google service accounts (each with 15 GiB) into one logical drive, overcoming per-Drive limits and allowing uploads of very large files (&gt; 14.7 GiB) with progress and parallel transfers.</p>
<p><em>New service accounts created after April 15 2025 no longer receive 15 GiB each; this tool targets existing accounts only.</em></p>
<section id="why-is-it-needed">
<h3>Why is it needed<a class="headerlink" href="#why-is-it-needed" title="Link to this heading">#</a></h3>
<p>Google Team Drives (Shared Drives) cap out around 100 GiB. By aggregating many service accounts, Sadrive provides a familiar “Drive” interface backed by ~15 GiB chunks.</p>
</section>
<section id="how-it-works">
<h3>How It Works<a class="headerlink" href="#how-it-works" title="Link to this heading">#</a></h3>
<ol class="arabic simple">
<li><p>You provision N service accounts (up to 100 per project).</p></li>
<li><p>Sadrive’s CLI detects which account has enough free space.</p></li>
<li><p>Files are routed and uploaded automatically—no more manual juggling.</p></li>
</ol>
</section>
<section id="key-benefits">
<h3>Key Benefits<a class="headerlink" href="#key-benefits" title="Link to this heading">#</a></h3>
<ul class="simple">
<li><p><strong>Mount</strong> your aggregated drive locally for read-only access</p></li>
<li><p><strong>Parallel</strong> uploads &amp; downloads (via gclone)</p></li>
<li><p><strong>Automatic</strong> splitting of files &gt; 14.7 GiB</p></li>
<li><p><strong>Progress</strong> bars, ETA, and fuzzy search built in</p></li>
<li><p><strong>Statically typed</strong> codebase with full type hints</p></li>
</ul>
</section>
<section id="examples">
<h3>Examples<a class="headerlink" href="#examples" title="Link to this heading">#</a></h3>
<ol class="arabic">
<li><p><code class="docutils literal notranslate"><span class="pre">sadrive</span></code></p>
<img alt="sadrive" src="_images/image.png" />
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">sadrive</span> <span class="pre">config</span> <span class="pre">set-dir</span> <span class="pre">&lt;path&gt;</span></code></p>
<img alt="sadrive config set-dir" src="_images/image-1.png" />
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">sadrive</span> <span class="pre">rename</span> <span class="pre">newname</span> <span class="pre">file/folderid</span></code></p>
<img alt="sadrive rename" src="_images/image-2.png" />
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">sadrive</span> <span class="pre">navigate</span> <span class="pre">[optional_folderid]</span></code></p>
<img alt="sadrive navigate" src="_images/image-3.png" />
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">sadrive</span> <span class="pre">share</span> <span class="pre">file/folderid</span></code></p>
<img alt="sadrive share" src="_images/image-4.png" />
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">sadrive</span> <span class="pre">mount</span></code> (read‑only filesystem)</p>
<img alt="sadrive mount" src="_images/image-5.png" />
<img alt="sadrive mount view" src="_images/image-6.png" />
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">sadrive</span> <span class="pre">delete</span> <span class="pre">file/folderid</span></code></p>
<img alt="sadrive delete" src="_images/image-7.png" />
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">sadrive</span> <span class="pre">newfolder</span> <span class="pre">name</span> <span class="pre">[optional_destination_id]</span></code></p>
<img alt="sadrive newfolder" src="_images/image-8.png" />
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">sadrive</span> <span class="pre">upload</span> <span class="pre">path/to/upload</span> <span class="pre">[destination_folder_id]</span></code>
<em>(If no destination ID is given, uploads to the parent in `config.json`.)</em></p>
<img alt="sadrive upload" src="_images/image-9.png" />
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">sadrive</span> <span class="pre">download</span> <span class="pre">folder_id</span> <span class="pre">path/to/destination</span> <span class="pre">[--transfers</span> <span class="pre">int]</span></code>
<em>(–transfers = max parallel downloads)</em></p>
<img alt="sadrive download" src="_images/image-10.png" />
</li>
</ol>
<div class="toctree-wrapper compound">
<p aria-level="2" class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="interface.html">Command-Line Interface</a><ul>
<li class="toctree-l2"><a class="reference internal" href="interface.html#sadrive">sadrive</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="apireference.html">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="modules/sadrive.cli.html">sadrive.cli module</a></li>
<li class="toctree-l2"><a class="reference internal" href="modules/sadrive.commands.html">sadrive.commands package</a></li>
<li class="toctree-l2"><a class="reference internal" href="modules/sadrive.commands.config.html">sadrive.commands.config module</a></li>
<li class="toctree-l2"><a class="reference internal" href="modules/sadrive.commands.db.html">sadrive.commands.db module</a></li>
<li class="toctree-l2"><a class="reference internal" href="modules/sadrive.commands.delete.html">sadrive.commands.delete module</a></li>
<li class="toctree-l2"><a class="reference internal" href="modules/sadrive.commands.downlaod.html">sadrive.commands.downlaod module</a></li>
<li class="toctree-l2"><a class="reference internal" href="modules/sadrive.commands.manipulation.html">sadrive.commands.manipulation module</a></li>
<li class="toctree-l2"><a class="reference internal" href="modules/sadrive.commands.navigate.html">sadrive.commands.navigate module</a></li>
<li class="toctree-l2"><a class="reference internal" href="modules/sadrive.commands.upload.html">sadrive.commands.upload module</a></li>
<li class="toctree-l2"><a class="reference internal" href="modules/sadrive.helpers.html">sadrive.helpers package</a></li>
<li class="toctree-l2"><a class="reference internal" href="modules/sadrive.helpers.dbf.html">sadrive.helpers.dbf module</a></li>
<li class="toctree-l2"><a class="reference internal" href="modules/sadrive.helpers.drive.html">sadrive.helpers.drive module</a></li>
<li class="toctree-l2"><a class="reference internal" href="modules/sadrive.helpers.utils.html">sadrive.helpers.utils module</a></li>
</ul>
</li>
</ul>
</div>
</section>
</section>
</section>
<section id="indices-and-tables">
<h1>Indices and tables<a class="headerlink" href="#indices-and-tables" title="Link to this heading">#</a></h1>
<ul class="simple">
<li><p><a class="reference internal" href="genindex.html"><span class="std std-ref">Index</span></a></p></li>
<li><p><a class="reference internal" href="py-modindex.html"><span class="std std-ref">Module Index</span></a></p></li>
<li><p><a class="reference internal" href="search.html"><span class="std std-ref">Search Page</span></a></p></li>
</ul>
</section>


                </article>
              
              
              
              
              
                <footer class="prev-next-footer d-print-none">
                  
<div class="prev-next-area">
    <a class="right-next"
       href="interface.html"
       title="next page">
      <div class="prev-next-info">
        <p class="prev-next-subtitle">next</p>
        <p class="prev-next-title">Command-Line Interface</p>
      </div>
      <i class="fa-solid fa-angle-right"></i>
    </a>
</div>
                </footer>
              
            </div>
            
            
              
                <dialog id="pst-secondary-sidebar-modal"></dialog>
                <div id="pst-secondary-sidebar" class="bd-sidebar-secondary bd-toc"><div class="sidebar-secondary-items sidebar-secondary__inner">


  <div class="sidebar-secondary-item">
<div
    id="pst-page-navigation-heading-2"
    class="page-toc tocsection onthispage">
    <i class="fa-solid fa-list"></i> On this page
  </div>
  <nav class="bd-toc-nav page-toc" aria-labelledby="pst-page-navigation-heading-2">
    <ul class="visible nav section-nav flex-column">
<li class="toc-h1 nav-item toc-entry"><a class="reference internal nav-link" href="#">Sadrive Documentation</a><ul class="visible nav section-nav flex-column">
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sa-drive-service-account-drive">SA Drive (Service Account Drive)</a><ul class="nav section-nav flex-column">
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#why-is-it-needed">Why is it needed</a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#how-it-works">How It Works</a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#key-benefits">Key Benefits</a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#examples">Examples</a></li>
</ul>
</li>
</ul>
</li>
<li class="toc-h1 nav-item toc-entry"><a class="reference internal nav-link" href="#indices-and-tables">Indices and tables</a></li>
</ul>

  </nav></div>

  <div class="sidebar-secondary-item">
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/index.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div></div>

</div></div>
              
            
          </div>
          <footer class="bd-footer-content">
            
          </footer>
        
      </main>
    </div>
  </div>
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script defer src="_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf"></script>
<script defer src="_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf"></script>

  <footer class="bd-footer">
<div class="bd-footer__inner bd-page-width">
  
    <div class="footer-items__start">
      
        <div class="footer-item">

  <p class="copyright">
    
      © Copyright 2025, jsmaskeen.
      <br/>
    
  </p>
</div>
      
        <div class="footer-item">

  <p class="sphinx-version">
    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    <br/>
  </p>
</div>
      
    </div>
  
  
  
    <div class="footer-items__end">
      
        <div class="footer-item">
<p class="theme-version">
  <!-- # L10n: Setting the PST URL as an argument as this does not need to be localized -->
  Built with the <a href="https://pydata-sphinx-theme.readthedocs.io/en/stable/index.html">PyData Sphinx Theme</a> 0.16.1.
</p></div>
      
    </div>
  
</div>

  </footer>
  </body>
</html>