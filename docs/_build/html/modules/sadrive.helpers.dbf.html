
<!DOCTYPE html>


<html lang="en" data-content_root="../" >

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>sadrive.helpers.dbf module &#8212; sadrive 2.0.0 documentation</title>
  
  
  
  <script data-cfasync="false">
    document.documentElement.dataset.mode = localStorage.getItem("mode") || "";
    document.documentElement.dataset.theme = localStorage.getItem("theme") || "";
  </script>
  <!--
    this give us a css class that will be invisible only if js is disabled
  -->
  <noscript>
    <style>
      .pst-js-only { display: none !important; }

    </style>
  </noscript>
  
  <!-- Loaded before other Sphinx assets -->
  <link href="../_static/styles/theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />
<link href="../_static/styles/pydata-sphinx-theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=8f2a1f02" />
  
  <!-- So that users can add custom icons -->
  <script src="../_static/scripts/fontawesome.js?digest=8878045cc6db502f8baf"></script>
  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf" />
<link rel="preload" as="script" href="../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf" />

    <script src="../_static/documentation_options.js?v=51b770b3"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script>DOCUMENTATION_OPTIONS.pagename = 'modules/sadrive.helpers.dbf';</script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="sadrive.helpers.drive module" href="sadrive.helpers.drive.html" />
    <link rel="prev" title="sadrive.helpers package" href="sadrive.helpers.html" />
  <meta name="viewport" content="width=device-width, initial-scale=1"/>
  <meta name="docsearch:language" content="en"/>
  <meta name="docsearch:version" content="" />
  </head>
  
  
  <body data-bs-spy="scroll" data-bs-target=".bd-toc-nav" data-offset="180" data-bs-root-margin="0px 0px -60%" data-default-mode="">

  
  
  <div id="pst-skip-link" class="skip-link d-print-none"><a href="#main-content">Skip to main content</a></div>
  
  <div id="pst-scroll-pixel-helper"></div>
  
  <button type="button" class="btn rounded-pill" id="pst-back-to-top">
    <i class="fa-solid fa-arrow-up"></i>Back to top</button>

  
  <dialog id="pst-search-dialog">
    
<form class="bd-search d-flex align-items-center"
      action="../search.html"
      method="get">
  <i class="fa-solid fa-magnifying-glass"></i>
  <input type="search"
         class="form-control"
         name="q"
         placeholder="Search the docs ..."
         aria-label="Search the docs ..."
         autocomplete="off"
         autocorrect="off"
         autocapitalize="off"
         spellcheck="false"/>
  <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd>K</kbd></span>
</form>
  </dialog>

  <div class="pst-async-banner-revealer d-none">
  <aside id="bd-header-version-warning" class="d-none d-print-none" aria-label="Version warning"></aside>
</div>

  
    <header class="bd-header navbar navbar-expand-lg bd-navbar d-print-none">
<div class="bd-header__inner bd-page-width">
  <button class="pst-navbar-icon sidebar-toggle primary-toggle" aria-label="Site navigation">
    <span class="fa-solid fa-bars"></span>
  </button>
  
  
  <div class="col-lg-3 navbar-header-items__start">
    
      <div class="navbar-item">

  
    
  

<a class="navbar-brand logo" href="../index.html">
  
  
  
  
  
  
    <p class="title logo__title">sadrive 2.0.0 documentation</p>
  
</a></div>
    
  </div>
  
  <div class="col-lg-9 navbar-header-items">
    
    <div class="me-auto navbar-header-items__center">
      
        <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item current active">
  <a class="nav-link nav-internal" href="../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
      
    </div>
    
    
    <div class="navbar-header-items__end">
      
        <div class="navbar-item navbar-persistent--container">
          

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
        </div>
      
      
        <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
      
    </div>
    
  </div>
  
  
    <div class="navbar-persistent--mobile">

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
    </div>
  

  
    <button class="pst-navbar-icon sidebar-toggle secondary-toggle" aria-label="On this page">
      <span class="fa-solid fa-outdent"></span>
    </button>
  
</div>

    </header>
  

  <div class="bd-container">
    <div class="bd-container__inner bd-page-width">
      
      
      
      <dialog id="pst-primary-sidebar-modal"></dialog>
      <div id="pst-primary-sidebar" class="bd-sidebar-primary bd-sidebar">
        

  
  <div class="sidebar-header-items sidebar-primary__section">
    
    
      <div class="sidebar-header-items__center">
        
          
          
            <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item current active">
  <a class="nav-link nav-internal" href="../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
          
        
      </div>
    
    
    
      <div class="sidebar-header-items__end">
        
          <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
        
      </div>
    
  </div>
  
    <div class="sidebar-primary-items__start sidebar-primary__section">
        <div class="sidebar-primary-item">
<nav class="bd-docs-nav bd-links"
     aria-label="Section Navigation">
  <p class="bd-links__title" role="heading" aria-level="1">Section Navigation</p>
  <div class="bd-toc-item navbar-nav"><ul class="current nav bd-sidenav">
<li class="toctree-l1"><a class="reference internal" href="sadrive.cli.html">sadrive.cli module</a></li>
<li class="toctree-l1 has-children"><a class="reference internal" href="sadrive.commands.html">sadrive.commands package</a><details><summary><span class="toctree-toggle" role="presentation"><i class="fa-solid fa-chevron-down"></i></span></summary><ul>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.config.html">sadrive.commands.config module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.db.html">sadrive.commands.db module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.delete.html">sadrive.commands.delete module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.downlaod.html">sadrive.commands.downlaod module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.manipulation.html">sadrive.commands.manipulation module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.navigate.html">sadrive.commands.navigate module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.upload.html">sadrive.commands.upload module</a></li>
</ul>
</details></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.config.html">sadrive.commands.config module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.db.html">sadrive.commands.db module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.delete.html">sadrive.commands.delete module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.downlaod.html">sadrive.commands.downlaod module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.manipulation.html">sadrive.commands.manipulation module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.navigate.html">sadrive.commands.navigate module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.upload.html">sadrive.commands.upload module</a></li>
<li class="toctree-l1 current active has-children"><a class="reference internal" href="sadrive.helpers.html">sadrive.helpers package</a><details open="open"><summary><span class="toctree-toggle" role="presentation"><i class="fa-solid fa-chevron-down"></i></span></summary><ul class="current">
<li class="toctree-l2 current active"><a class="current reference internal" href="#">sadrive.helpers.dbf module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.helpers.drive.html">sadrive.helpers.drive module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.helpers.utils.html">sadrive.helpers.utils module</a></li>
</ul>
</details></li>
<li class="toctree-l1 current active"><a class="current reference internal" href="#">sadrive.helpers.dbf module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.helpers.drive.html">sadrive.helpers.drive module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.helpers.utils.html">sadrive.helpers.utils module</a></li>
</ul>
</div>
</nav></div>
    </div>
  
  
  <div class="sidebar-primary-items__end sidebar-primary__section">
      <div class="sidebar-primary-item">
<div id="ethical-ad-placement"
      class="flat"
      data-ea-publisher="readthedocs"
      data-ea-type="readthedocs-sidebar"
      data-ea-manual="true">
</div></div>
  </div>


      </div>
      
      <main id="main-content" class="bd-main" role="main">
        
        
          <div class="bd-content">
            <div class="bd-article-container">
              
              <div class="bd-header-article d-print-none">
<div class="header-article-items header-article__inner">
  
    <div class="header-article-items__start">
      
        <div class="header-article-item">

<nav aria-label="Breadcrumb" class="d-print-none">
  <ul class="bd-breadcrumbs">
    
    <li class="breadcrumb-item breadcrumb-home">
      <a href="../index.html" class="nav-link" aria-label="Home">
        <i class="fa-solid fa-home"></i>
      </a>
    </li>
    
    <li class="breadcrumb-item"><a href="../apireference.html" class="nav-link">API Reference</a></li>
    
    
    <li class="breadcrumb-item"><a href="sadrive.helpers.html" class="nav-link">sadrive.helpers package</a></li>
    
    <li class="breadcrumb-item active" aria-current="page"><span class="ellipsis">sadrive.helpers.dbf module</span></li>
  </ul>
</nav>
</div>
      
    </div>
  
  
</div>
</div>
              
              
              
                
<div id="searchbox"></div>
                <article class="bd-article">
                  
  <section id="module-sadrive.helpers.dbf">
<span id="sadrive-helpers-dbf-module"></span><h1>sadrive.helpers.dbf module<a class="headerlink" href="#module-sadrive.helpers.dbf" title="Link to this heading">#</a></h1>
<p>Database operations module for the CLI application.</p>
<p>This module manages SQLite connection, schema initialization, and CRUD operations
for file mappings and service account size tracking.</p>
<p>Provides:
- Connection management
- Table creation for file_map and sa_size_map
- Insert, update, delete, and query functions for file entries
- Service account size tracking and management
- Utility functions for searching and space reporting</p>
<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.add_size">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">add_size</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sa_num</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">syncing</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">False</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#add_size"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.add_size" title="Link to this definition">#</a></dt>
<dd><p>Updates a service account’s size, adding or syncing.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>sa_num</strong> – ID of the account to update.</p></li>
<li><p><strong>size</strong> – Size change in bytes.</p></li>
<li><p><strong>syncing</strong> – If True, sets size exactly to ‘size’.</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.clear_file_map">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">clear_file_map</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#clear_file_map"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.clear_file_map" title="Link to this definition">#</a></dt>
<dd><p>Deletes all records from file_map.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.delete_file">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">delete_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#delete_file"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.delete_file" title="Link to this definition">#</a></dt>
<dd><p>Deletes a file record and subtracts its size from the service account.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>file_id</strong> – ID of the file to remove.</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.find_children">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">find_children</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#find_children"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.find_children" title="Link to this definition">#</a></dt>
<dd><p>Lists file_map entries with the given parent_id.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>parent_id</strong> – Parent folder identifier.</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>File records under the parent.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>list of dict</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.folder_exists">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">folder_exists</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#folder_exists"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.folder_exists" title="Link to this definition">#</a></dt>
<dd><p>Checks if a folder entry exists under a parent.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>name</strong> – Folder name to look for.</p></li>
<li><p><strong>parent_id</strong> – Parent folder identifier.</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Folder ID if exists, else None.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>str or None</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.get_connection">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">get_connection</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#get_connection"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.get_connection" title="Link to this definition">#</a></dt>
<dd><p>Opens a SQLite connection to the configured database.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Connection object with row_factory set to sqlite3.Row.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>sqlite3.Connection</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.get_file_details">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">get_file_details</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#get_file_details"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.get_file_details" title="Link to this definition">#</a></dt>
<dd><p>Retrieves a file record by its ID.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>file_id</strong> – Unique identifier to look up.</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>File record fields, or None if not found.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>dict</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.get_sa_num">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">get_sa_num</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">email</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#get_sa_num"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.get_sa_num" title="Link to this definition">#</a></dt>
<dd><p>Finds a service account ID by its email.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>email</strong> – Client email to search.</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Account ID if found.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>str or None</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.get_sa_size_taken">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">get_sa_size_taken</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sa_num</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#get_sa_size_taken"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.get_sa_size_taken" title="Link to this definition">#</a></dt>
<dd><p>Retrieves size and email for a given service account.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>sa_num</strong> – Service account identifier.</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Record fields, or None if not found.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>dict</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.get_size_map">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">get_size_map</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#get_size_map"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.get_size_map" title="Link to this definition">#</a></dt>
<dd><p>Retrieves all service account size records.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Each dict has ‘_id’, ‘size’, and ‘email’.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>list of dict</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.insert_file">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">insert_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file_name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file_size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">service_acc_num</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shared</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#insert_file"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.insert_file" title="Link to this definition">#</a></dt>
<dd><p>Inserts a new file record into file_map and updates account size.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>file_id</strong> – Unique identifier of the file in Drive.</p></li>
<li><p><strong>file_name</strong> – Name of the file.</p></li>
<li><p><strong>parent_id</strong> – Parent folder identifier.</p></li>
<li><p><strong>file_size</strong> – Size of the file in bytes.</p></li>
<li><p><strong>type</strong> – “file” or “folder”.</p></li>
<li><p><strong>service_acc_num</strong> – ID of the service account used.</p></li>
<li><p><strong>shared</strong> – Whether the file is shared.</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.insert_size_map">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">insert_size_map</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sa_num</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">0</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#insert_size_map"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.insert_size_map" title="Link to this definition">#</a></dt>
<dd><p>Inserts a new entry in sa_size_map with initial size and email from account file.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>sa_num</strong> – Service account identifier.</p></li>
<li><p><strong>size</strong> – Initial size in bytes. Default is 0.</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.remove_size">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">remove_size</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sa_num</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#remove_size"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.remove_size" title="Link to this definition">#</a></dt>
<dd><p>Subtracts size from a service account’s recorded usage.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>sa_num</strong> – Account ID.</p></li>
<li><p><strong>size</strong> – Bytes to subtract.</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.rename_file">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">rename_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_file_name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#rename_file"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.rename_file" title="Link to this definition">#</a></dt>
<dd><p>Updates the name of a file record.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>file_id</strong> – ID of the file to rename.</p></li>
<li><p><strong>new_file_name</strong> – New name to assign.</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.reset_sa_sizes">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">reset_sa_sizes</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#reset_sa_sizes"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.reset_sa_sizes" title="Link to this definition">#</a></dt>
<dd><p>Resets all sizes in sa_size_map to zero.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.search_for_file_contains">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">search_for_file_contains</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">value</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#search_for_file_contains"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.search_for_file_contains" title="Link to this definition">#</a></dt>
<dd><p>Searches file_map for filenames containing a substring.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>value</strong> – Substring to search in file_name.</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Matching file records.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>list of dict</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.share_file">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">share_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">shared</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">bool</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">True</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#share_file"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.share_file" title="Link to this definition">#</a></dt>
<dd><p>Marks a file as shared or unshared.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>file_id</strong> – ID of the file.</p></li>
<li><p><strong>shared</strong> – True to mark shared, False otherwise.</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.dbf.space_details">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.dbf.</span></span><span class="sig-name descname"><span class="pre">space_details</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/dbf.html#space_details"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.dbf.space_details" title="Link to this definition">#</a></dt>
<dd><p>Returns aggregated occupied and available space across accounts.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Total used bytes.
available (int): Total available capacity in bytes.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>occupied (int)</p>
</dd>
</dl>
</dd></dl>

</section>


                </article>
              
              
              
              
              
                <footer class="prev-next-footer d-print-none">
                  
<div class="prev-next-area">
    <a class="left-prev"
       href="sadrive.helpers.html"
       title="previous page">
      <i class="fa-solid fa-angle-left"></i>
      <div class="prev-next-info">
        <p class="prev-next-subtitle">previous</p>
        <p class="prev-next-title">sadrive.helpers package</p>
      </div>
    </a>
    <a class="right-next"
       href="sadrive.helpers.drive.html"
       title="next page">
      <div class="prev-next-info">
        <p class="prev-next-subtitle">next</p>
        <p class="prev-next-title">sadrive.helpers.drive module</p>
      </div>
      <i class="fa-solid fa-angle-right"></i>
    </a>
</div>
                </footer>
              
            </div>
            
            
              
                <dialog id="pst-secondary-sidebar-modal"></dialog>
                <div id="pst-secondary-sidebar" class="bd-sidebar-secondary bd-toc"><div class="sidebar-secondary-items sidebar-secondary__inner">


  <div class="sidebar-secondary-item">
<div
    id="pst-page-navigation-heading-2"
    class="page-toc tocsection onthispage">
    <i class="fa-solid fa-list"></i> On this page
  </div>
  <nav class="bd-toc-nav page-toc" aria-labelledby="pst-page-navigation-heading-2">
    <ul class="visible nav section-nav flex-column">
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.add_size"><code class="docutils literal notranslate"><span class="pre">add_size()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.clear_file_map"><code class="docutils literal notranslate"><span class="pre">clear_file_map()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.delete_file"><code class="docutils literal notranslate"><span class="pre">delete_file()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.find_children"><code class="docutils literal notranslate"><span class="pre">find_children()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.folder_exists"><code class="docutils literal notranslate"><span class="pre">folder_exists()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.get_connection"><code class="docutils literal notranslate"><span class="pre">get_connection()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.get_file_details"><code class="docutils literal notranslate"><span class="pre">get_file_details()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.get_sa_num"><code class="docutils literal notranslate"><span class="pre">get_sa_num()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.get_sa_size_taken"><code class="docutils literal notranslate"><span class="pre">get_sa_size_taken()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.get_size_map"><code class="docutils literal notranslate"><span class="pre">get_size_map()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.insert_file"><code class="docutils literal notranslate"><span class="pre">insert_file()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.insert_size_map"><code class="docutils literal notranslate"><span class="pre">insert_size_map()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.remove_size"><code class="docutils literal notranslate"><span class="pre">remove_size()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.rename_file"><code class="docutils literal notranslate"><span class="pre">rename_file()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.reset_sa_sizes"><code class="docutils literal notranslate"><span class="pre">reset_sa_sizes()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.search_for_file_contains"><code class="docutils literal notranslate"><span class="pre">search_for_file_contains()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.share_file"><code class="docutils literal notranslate"><span class="pre">share_file()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.dbf.space_details"><code class="docutils literal notranslate"><span class="pre">space_details()</span></code></a></li>
</ul>
  </nav></div>

  <div class="sidebar-secondary-item">
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/modules/sadrive.helpers.dbf.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div></div>

</div></div>
              
            
          </div>
          <footer class="bd-footer-content">
            
          </footer>
        
      </main>
    </div>
  </div>
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script defer src="../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf"></script>
<script defer src="../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf"></script>

  <footer class="bd-footer">
<div class="bd-footer__inner bd-page-width">
  
    <div class="footer-items__start">
      
        <div class="footer-item">

  <p class="copyright">
    
      © Copyright 2025, jsmaskeen.
      <br/>
    
  </p>
</div>
      
        <div class="footer-item">

  <p class="sphinx-version">
    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    <br/>
  </p>
</div>
      
    </div>
  
  
  
    <div class="footer-items__end">
      
        <div class="footer-item">
<p class="theme-version">
  <!-- # L10n: Setting the PST URL as an argument as this does not need to be localized -->
  Built with the <a href="https://pydata-sphinx-theme.readthedocs.io/en/stable/index.html">PyData Sphinx Theme</a> 0.16.1.
</p></div>
      
    </div>
  
</div>

  </footer>
  </body>
</html>