
<!DOCTYPE html>


<html lang="en" data-content_root="../" >

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>sadrive.helpers.drive module &#8212; sadrive 2.0.0 documentation</title>
  
  
  
  <script data-cfasync="false">
    document.documentElement.dataset.mode = localStorage.getItem("mode") || "";
    document.documentElement.dataset.theme = localStorage.getItem("theme") || "";
  </script>
  <!--
    this give us a css class that will be invisible only if js is disabled
  -->
  <noscript>
    <style>
      .pst-js-only { display: none !important; }

    </style>
  </noscript>
  
  <!-- Loaded before other Sphinx assets -->
  <link href="../_static/styles/theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />
<link href="../_static/styles/pydata-sphinx-theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=8f2a1f02" />
  
  <!-- So that users can add custom icons -->
  <script src="../_static/scripts/fontawesome.js?digest=8878045cc6db502f8baf"></script>
  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf" />
<link rel="preload" as="script" href="../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf" />

    <script src="../_static/documentation_options.js?v=51b770b3"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script>DOCUMENTATION_OPTIONS.pagename = 'modules/sadrive.helpers.drive';</script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="sadrive.helpers.utils module" href="sadrive.helpers.utils.html" />
    <link rel="prev" title="sadrive.helpers.dbf module" href="sadrive.helpers.dbf.html" />
  <meta name="viewport" content="width=device-width, initial-scale=1"/>
  <meta name="docsearch:language" content="en"/>
  <meta name="docsearch:version" content="" />
  </head>
  
  
  <body data-bs-spy="scroll" data-bs-target=".bd-toc-nav" data-offset="180" data-bs-root-margin="0px 0px -60%" data-default-mode="">

  
  
  <div id="pst-skip-link" class="skip-link d-print-none"><a href="#main-content">Skip to main content</a></div>
  
  <div id="pst-scroll-pixel-helper"></div>
  
  <button type="button" class="btn rounded-pill" id="pst-back-to-top">
    <i class="fa-solid fa-arrow-up"></i>Back to top</button>

  
  <dialog id="pst-search-dialog">
    
<form class="bd-search d-flex align-items-center"
      action="../search.html"
      method="get">
  <i class="fa-solid fa-magnifying-glass"></i>
  <input type="search"
         class="form-control"
         name="q"
         placeholder="Search the docs ..."
         aria-label="Search the docs ..."
         autocomplete="off"
         autocorrect="off"
         autocapitalize="off"
         spellcheck="false"/>
  <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd>K</kbd></span>
</form>
  </dialog>

  <div class="pst-async-banner-revealer d-none">
  <aside id="bd-header-version-warning" class="d-none d-print-none" aria-label="Version warning"></aside>
</div>

  
    <header class="bd-header navbar navbar-expand-lg bd-navbar d-print-none">
<div class="bd-header__inner bd-page-width">
  <button class="pst-navbar-icon sidebar-toggle primary-toggle" aria-label="Site navigation">
    <span class="fa-solid fa-bars"></span>
  </button>
  
  
  <div class="col-lg-3 navbar-header-items__start">
    
      <div class="navbar-item">

  
    
  

<a class="navbar-brand logo" href="../index.html">
  
  
  
  
  
  
    <p class="title logo__title">sadrive 2.0.0 documentation</p>
  
</a></div>
    
  </div>
  
  <div class="col-lg-9 navbar-header-items">
    
    <div class="me-auto navbar-header-items__center">
      
        <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item current active">
  <a class="nav-link nav-internal" href="../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
      
    </div>
    
    
    <div class="navbar-header-items__end">
      
        <div class="navbar-item navbar-persistent--container">
          

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
        </div>
      
      
        <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
      
    </div>
    
  </div>
  
  
    <div class="navbar-persistent--mobile">

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
    </div>
  

  
    <button class="pst-navbar-icon sidebar-toggle secondary-toggle" aria-label="On this page">
      <span class="fa-solid fa-outdent"></span>
    </button>
  
</div>

    </header>
  

  <div class="bd-container">
    <div class="bd-container__inner bd-page-width">
      
      
      
      <dialog id="pst-primary-sidebar-modal"></dialog>
      <div id="pst-primary-sidebar" class="bd-sidebar-primary bd-sidebar">
        

  
  <div class="sidebar-header-items sidebar-primary__section">
    
    
      <div class="sidebar-header-items__center">
        
          
          
            <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item current active">
  <a class="nav-link nav-internal" href="../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
          
        
      </div>
    
    
    
      <div class="sidebar-header-items__end">
        
          <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
        
      </div>
    
  </div>
  
    <div class="sidebar-primary-items__start sidebar-primary__section">
        <div class="sidebar-primary-item">
<nav class="bd-docs-nav bd-links"
     aria-label="Section Navigation">
  <p class="bd-links__title" role="heading" aria-level="1">Section Navigation</p>
  <div class="bd-toc-item navbar-nav"><ul class="current nav bd-sidenav">
<li class="toctree-l1"><a class="reference internal" href="sadrive.cli.html">sadrive.cli module</a></li>
<li class="toctree-l1 has-children"><a class="reference internal" href="sadrive.commands.html">sadrive.commands package</a><details><summary><span class="toctree-toggle" role="presentation"><i class="fa-solid fa-chevron-down"></i></span></summary><ul>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.config.html">sadrive.commands.config module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.db.html">sadrive.commands.db module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.delete.html">sadrive.commands.delete module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.downlaod.html">sadrive.commands.downlaod module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.manipulation.html">sadrive.commands.manipulation module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.navigate.html">sadrive.commands.navigate module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.upload.html">sadrive.commands.upload module</a></li>
</ul>
</details></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.config.html">sadrive.commands.config module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.db.html">sadrive.commands.db module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.delete.html">sadrive.commands.delete module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.downlaod.html">sadrive.commands.downlaod module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.manipulation.html">sadrive.commands.manipulation module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.navigate.html">sadrive.commands.navigate module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.upload.html">sadrive.commands.upload module</a></li>
<li class="toctree-l1 current active has-children"><a class="reference internal" href="sadrive.helpers.html">sadrive.helpers package</a><details open="open"><summary><span class="toctree-toggle" role="presentation"><i class="fa-solid fa-chevron-down"></i></span></summary><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="sadrive.helpers.dbf.html">sadrive.helpers.dbf module</a></li>
<li class="toctree-l2 current active"><a class="current reference internal" href="#">sadrive.helpers.drive module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.helpers.utils.html">sadrive.helpers.utils module</a></li>
</ul>
</details></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.helpers.dbf.html">sadrive.helpers.dbf module</a></li>
<li class="toctree-l1 current active"><a class="current reference internal" href="#">sadrive.helpers.drive module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.helpers.utils.html">sadrive.helpers.utils module</a></li>
</ul>
</div>
</nav></div>
    </div>
  
  
  <div class="sidebar-primary-items__end sidebar-primary__section">
      <div class="sidebar-primary-item">
<div id="ethical-ad-placement"
      class="flat"
      data-ea-publisher="readthedocs"
      data-ea-type="readthedocs-sidebar"
      data-ea-manual="true">
</div></div>
  </div>


      </div>
      
      <main id="main-content" class="bd-main" role="main">
        
        
          <div class="bd-content">
            <div class="bd-article-container">
              
              <div class="bd-header-article d-print-none">
<div class="header-article-items header-article__inner">
  
    <div class="header-article-items__start">
      
        <div class="header-article-item">

<nav aria-label="Breadcrumb" class="d-print-none">
  <ul class="bd-breadcrumbs">
    
    <li class="breadcrumb-item breadcrumb-home">
      <a href="../index.html" class="nav-link" aria-label="Home">
        <i class="fa-solid fa-home"></i>
      </a>
    </li>
    
    <li class="breadcrumb-item"><a href="../apireference.html" class="nav-link">API Reference</a></li>
    
    
    <li class="breadcrumb-item"><a href="sadrive.helpers.html" class="nav-link">sadrive.helpers package</a></li>
    
    <li class="breadcrumb-item active" aria-current="page"><span class="ellipsis">sadrive.helpers.drive module</span></li>
  </ul>
</nav>
</div>
      
    </div>
  
  
</div>
</div>
              
              
              
                
<div id="searchbox"></div>
                <article class="bd-article">
                  
  <section id="module-sadrive.helpers.drive">
<span id="sadrive-helpers-drive-module"></span><h1>sadrive.helpers.drive module<a class="headerlink" href="#module-sadrive.helpers.drive" title="Link to this heading">#</a></h1>
<p>Provides Google Drive client wrapper using service accounts for authentication.</p>
<p>Includes:
- PatchedDrive subclass for typing
- SADrive class for Drive operations
- Authentication
- File listing, creation, upload, rename, delete
- Sharing/unsharing
- Search and space usage
- Bulk deletion</p>
<p>Constants:
- PARENT_ID: Root folder ID from config</p>
<dl class="py class">
<dt class="sig sig-object py" id="sadrive.helpers.drive.PatchedDrive">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sadrive.helpers.drive.</span></span><span class="sig-name descname"><span class="pre">PatchedDrive</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">auth</span></span><span class="o"><span class="pre">=</span></span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/drive.html#PatchedDrive"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.drive.PatchedDrive" title="Link to this definition">#</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">GoogleDrive</span></code></p>
<p>Subclass of GoogleDrive with explicit auth attribute for type checking.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sadrive.helpers.drive.PatchedDrive.auth">
<span class="sig-name descname"><span class="pre">auth</span></span><a class="headerlink" href="#sadrive.helpers.drive.PatchedDrive.auth" title="Link to this definition">#</a></dt>
<dd><p>GoogleAuth instance used for authentication.</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>pydrive2.auth.GoogleAuth</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id0">
<span class="sig-name descname"><span class="pre">auth</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">GoogleAuth</span></em><a class="headerlink" href="#id0" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sadrive.helpers.drive.SADrive">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sadrive.helpers.drive.</span></span><span class="sig-name descname"><span class="pre">SADrive</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">service_account_num</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/drive.html#SADrive"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.drive.SADrive" title="Link to this definition">#</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Service-account-driven Google Drive client.</p>
<p>Uses pydrive2 and googleapiclient to perform common operations.</p>
<dl class="py method">
<dt class="sig sig-object py" id="sadrive.helpers.drive.SADrive.authorise">
<span class="sig-name descname"><span class="pre">authorise</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><a class="reference internal" href="#sadrive.helpers.drive.PatchedDrive" title="sadrive.helpers.drive.PatchedDrive"><span class="pre">PatchedDrive</span></a></span></span><a class="reference internal" href="../_modules/sadrive/helpers/drive.html#SADrive.authorise"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.drive.SADrive.authorise" title="Link to this definition">#</a></dt>
<dd><p>Authenticates using a service account JSON.</p>
<p>Uses pydrive2.GoogleAuth with service backend settings.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>An authenticated PatchedDrive instance.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sadrive.helpers.drive.SADrive.create_folder">
<span class="sig-name descname"><span class="pre">create_folder</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">subfolder_name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent_folder_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'root'</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="../_modules/sadrive/helpers/drive.html#SADrive.create_folder"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.drive.SADrive.create_folder" title="Link to this definition">#</a></dt>
<dd><p>Creates a new folder in Drive.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>subfolder_name</strong> – Name for the new folder.</p></li>
<li><p><strong>parent_folder_id</strong> – ID of the parent folder (default ‘root’).</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>The ID of the created folder.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sadrive.helpers.drive.SADrive.delete_all_files">
<span class="sig-name descname"><span class="pre">delete_all_files</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/drive.html#SADrive.delete_all_files"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.drive.SADrive.delete_all_files" title="Link to this definition">#</a></dt>
<dd><p>Permanently deletes all non-trashed files owned by the account.</p>
<p>Iterates pages of up to 1000 items, printing progress to stdout.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sadrive.helpers.drive.SADrive.delete_file">
<span class="sig-name descname"><span class="pre">delete_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/drive.html#SADrive.delete_file"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.drive.SADrive.delete_file" title="Link to this definition">#</a></dt>
<dd><p>Deletes a file in Drive.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>file_id</strong> – ID of the file to remove.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sadrive.helpers.drive.SADrive.list_files">
<span class="sig-name descname"><span class="pre">list_files</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">parent_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">'root'</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/drive.html#SADrive.list_files"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.drive.SADrive.list_files" title="Link to this definition">#</a></dt>
<dd><p>Lists non-trashed files under a given folder.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>parent_id</strong> – Drive folder ID (default: ‘root’).</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>List of GoogleDriveFile instances.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sadrive.helpers.drive.SADrive.rename">
<span class="sig-name descname"><span class="pre">rename</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fileid</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">new_name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/drive.html#SADrive.rename"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.drive.SADrive.rename" title="Link to this definition">#</a></dt>
<dd><p>Renames an existing Drive file.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>fileid</strong> – ID of the file to rename.</p></li>
<li><p><strong>new_name</strong> – New title for the file.</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>The updated GoogleDriveFile instance.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sadrive.helpers.drive.SADrive.search">
<span class="sig-name descname"><span class="pre">search</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/drive.html#SADrive.search"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.drive.SADrive.search" title="Link to this definition">#</a></dt>
<dd><p>Searches for files whose titles contain a substring.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>name</strong> – Substring to match in file titles.</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>List of dict representations of matched files.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sadrive.helpers.drive.SADrive.share">
<span class="sig-name descname"><span class="pre">share</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fileid</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/drive.html#SADrive.share"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.drive.SADrive.share" title="Link to this definition">#</a></dt>
<dd><p>Publishes a file by granting ‘reader’ permission to anyone.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>fileid</strong> – ID of the file to share.</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>The file’s alternateLink.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sadrive.helpers.drive.SADrive.unshare">
<span class="sig-name descname"><span class="pre">unshare</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/drive.html#SADrive.unshare"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.drive.SADrive.unshare" title="Link to this definition">#</a></dt>
<dd><p>Revokes ‘anyone’ permission from a file.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>file_id</strong> – ID of the file to unshare.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sadrive.helpers.drive.SADrive.upload_file">
<span class="sig-name descname"><span class="pre">upload_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">filename</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent_folder_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">stream_bytes</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">IOBase</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/drive.html#SADrive.upload_file"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.drive.SADrive.upload_file" title="Link to this definition">#</a></dt>
<dd><p>Uploads a file stream to Drive with resumable media.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>filename</strong> – Name to assign in Drive.</p></li>
<li><p><strong>parent_folder_id</strong> – ID of the destination folder.</p></li>
<li><p><strong>stream_bytes</strong> – File-like object providing binary data.</p></li>
</ul>
</dd>
<dt class="field-even">Yields<span class="colon">:</span></dt>
<dd class="field-even"><p>Progress percentage integers until upload completes.</p>
</dd>
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The upload response dict from the Drive API.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sadrive.helpers.drive.SADrive.used_space">
<span class="sig-name descname"><span class="pre">used_space</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/drive.html#SADrive.used_space"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.drive.SADrive.used_space" title="Link to this definition">#</a></dt>
<dd><p>Retrieves the total bytes used by the authenticated account.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Bytes used as an integer.</p>
</dd>
</dl>
</dd></dl>

</dd></dl>

</section>


                </article>
              
              
              
              
              
                <footer class="prev-next-footer d-print-none">
                  
<div class="prev-next-area">
    <a class="left-prev"
       href="sadrive.helpers.dbf.html"
       title="previous page">
      <i class="fa-solid fa-angle-left"></i>
      <div class="prev-next-info">
        <p class="prev-next-subtitle">previous</p>
        <p class="prev-next-title">sadrive.helpers.dbf module</p>
      </div>
    </a>
    <a class="right-next"
       href="sadrive.helpers.utils.html"
       title="next page">
      <div class="prev-next-info">
        <p class="prev-next-subtitle">next</p>
        <p class="prev-next-title">sadrive.helpers.utils module</p>
      </div>
      <i class="fa-solid fa-angle-right"></i>
    </a>
</div>
                </footer>
              
            </div>
            
            
              
                <dialog id="pst-secondary-sidebar-modal"></dialog>
                <div id="pst-secondary-sidebar" class="bd-sidebar-secondary bd-toc"><div class="sidebar-secondary-items sidebar-secondary__inner">


  <div class="sidebar-secondary-item">
<div
    id="pst-page-navigation-heading-2"
    class="page-toc tocsection onthispage">
    <i class="fa-solid fa-list"></i> On this page
  </div>
  <nav class="bd-toc-nav page-toc" aria-labelledby="pst-page-navigation-heading-2">
    <ul class="visible nav section-nav flex-column">
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.drive.PatchedDrive"><code class="docutils literal notranslate"><span class="pre">PatchedDrive</span></code></a><ul class="nav section-nav flex-column">
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.drive.PatchedDrive.auth"><code class="docutils literal notranslate"><span class="pre">PatchedDrive.auth</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#id0"><code class="docutils literal notranslate"><span class="pre">PatchedDrive.auth</span></code></a></li>
</ul>
</li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.drive.SADrive"><code class="docutils literal notranslate"><span class="pre">SADrive</span></code></a><ul class="nav section-nav flex-column">
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.drive.SADrive.authorise"><code class="docutils literal notranslate"><span class="pre">SADrive.authorise()</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.drive.SADrive.create_folder"><code class="docutils literal notranslate"><span class="pre">SADrive.create_folder()</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.drive.SADrive.delete_all_files"><code class="docutils literal notranslate"><span class="pre">SADrive.delete_all_files()</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.drive.SADrive.delete_file"><code class="docutils literal notranslate"><span class="pre">SADrive.delete_file()</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.drive.SADrive.list_files"><code class="docutils literal notranslate"><span class="pre">SADrive.list_files()</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.drive.SADrive.rename"><code class="docutils literal notranslate"><span class="pre">SADrive.rename()</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.drive.SADrive.search"><code class="docutils literal notranslate"><span class="pre">SADrive.search()</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.drive.SADrive.share"><code class="docutils literal notranslate"><span class="pre">SADrive.share()</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.drive.SADrive.unshare"><code class="docutils literal notranslate"><span class="pre">SADrive.unshare()</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.drive.SADrive.upload_file"><code class="docutils literal notranslate"><span class="pre">SADrive.upload_file()</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.drive.SADrive.used_space"><code class="docutils literal notranslate"><span class="pre">SADrive.used_space()</span></code></a></li>
</ul>
</li>
</ul>
  </nav></div>

  <div class="sidebar-secondary-item">
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/modules/sadrive.helpers.drive.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div></div>

</div></div>
              
            
          </div>
          <footer class="bd-footer-content">
            
          </footer>
        
      </main>
    </div>
  </div>
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script defer src="../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf"></script>
<script defer src="../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf"></script>

  <footer class="bd-footer">
<div class="bd-footer__inner bd-page-width">
  
    <div class="footer-items__start">
      
        <div class="footer-item">

  <p class="copyright">
    
      © Copyright 2025, jsmaskeen.
      <br/>
    
  </p>
</div>
      
        <div class="footer-item">

  <p class="sphinx-version">
    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    <br/>
  </p>
</div>
      
    </div>
  
  
  
    <div class="footer-items__end">
      
        <div class="footer-item">
<p class="theme-version">
  <!-- # L10n: Setting the PST URL as an argument as this does not need to be localized -->
  Built with the <a href="https://pydata-sphinx-theme.readthedocs.io/en/stable/index.html">PyData Sphinx Theme</a> 0.16.1.
</p></div>
      
    </div>
  
</div>

  </footer>
  </body>
</html>