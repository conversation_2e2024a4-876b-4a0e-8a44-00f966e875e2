
<!DOCTYPE html>


<html lang="en" data-content_root="../" >

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>sadrive.commands.upload module &#8212; sadrive 2.0.0 documentation</title>
  
  
  
  <script data-cfasync="false">
    document.documentElement.dataset.mode = localStorage.getItem("mode") || "";
    document.documentElement.dataset.theme = localStorage.getItem("theme") || "";
  </script>
  <!--
    this give us a css class that will be invisible only if js is disabled
  -->
  <noscript>
    <style>
      .pst-js-only { display: none !important; }

    </style>
  </noscript>
  
  <!-- Loaded before other Sphinx assets -->
  <link href="../_static/styles/theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />
<link href="../_static/styles/pydata-sphinx-theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=8f2a1f02" />
  
  <!-- So that users can add custom icons -->
  <script src="../_static/scripts/fontawesome.js?digest=8878045cc6db502f8baf"></script>
  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf" />
<link rel="preload" as="script" href="../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf" />

    <script src="../_static/documentation_options.js?v=51b770b3"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script>DOCUMENTATION_OPTIONS.pagename = 'modules/sadrive.commands.upload';</script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="sadrive.helpers package" href="sadrive.helpers.html" />
    <link rel="prev" title="sadrive.commands.navigate module" href="sadrive.commands.navigate.html" />
  <meta name="viewport" content="width=device-width, initial-scale=1"/>
  <meta name="docsearch:language" content="en"/>
  <meta name="docsearch:version" content="" />
  </head>
  
  
  <body data-bs-spy="scroll" data-bs-target=".bd-toc-nav" data-offset="180" data-bs-root-margin="0px 0px -60%" data-default-mode="">

  
  
  <div id="pst-skip-link" class="skip-link d-print-none"><a href="#main-content">Skip to main content</a></div>
  
  <div id="pst-scroll-pixel-helper"></div>
  
  <button type="button" class="btn rounded-pill" id="pst-back-to-top">
    <i class="fa-solid fa-arrow-up"></i>Back to top</button>

  
  <dialog id="pst-search-dialog">
    
<form class="bd-search d-flex align-items-center"
      action="../search.html"
      method="get">
  <i class="fa-solid fa-magnifying-glass"></i>
  <input type="search"
         class="form-control"
         name="q"
         placeholder="Search the docs ..."
         aria-label="Search the docs ..."
         autocomplete="off"
         autocorrect="off"
         autocapitalize="off"
         spellcheck="false"/>
  <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd>K</kbd></span>
</form>
  </dialog>

  <div class="pst-async-banner-revealer d-none">
  <aside id="bd-header-version-warning" class="d-none d-print-none" aria-label="Version warning"></aside>
</div>

  
    <header class="bd-header navbar navbar-expand-lg bd-navbar d-print-none">
<div class="bd-header__inner bd-page-width">
  <button class="pst-navbar-icon sidebar-toggle primary-toggle" aria-label="Site navigation">
    <span class="fa-solid fa-bars"></span>
  </button>
  
  
  <div class="col-lg-3 navbar-header-items__start">
    
      <div class="navbar-item">

  
    
  

<a class="navbar-brand logo" href="../index.html">
  
  
  
  
  
  
    <p class="title logo__title">sadrive 2.0.0 documentation</p>
  
</a></div>
    
  </div>
  
  <div class="col-lg-9 navbar-header-items">
    
    <div class="me-auto navbar-header-items__center">
      
        <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item current active">
  <a class="nav-link nav-internal" href="../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
      
    </div>
    
    
    <div class="navbar-header-items__end">
      
        <div class="navbar-item navbar-persistent--container">
          

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
        </div>
      
      
        <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
      
    </div>
    
  </div>
  
  
    <div class="navbar-persistent--mobile">

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
    </div>
  

  
    <button class="pst-navbar-icon sidebar-toggle secondary-toggle" aria-label="On this page">
      <span class="fa-solid fa-outdent"></span>
    </button>
  
</div>

    </header>
  

  <div class="bd-container">
    <div class="bd-container__inner bd-page-width">
      
      
      
      <dialog id="pst-primary-sidebar-modal"></dialog>
      <div id="pst-primary-sidebar" class="bd-sidebar-primary bd-sidebar">
        

  
  <div class="sidebar-header-items sidebar-primary__section">
    
    
      <div class="sidebar-header-items__center">
        
          
          
            <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item current active">
  <a class="nav-link nav-internal" href="../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
          
        
      </div>
    
    
    
      <div class="sidebar-header-items__end">
        
          <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
        
      </div>
    
  </div>
  
    <div class="sidebar-primary-items__start sidebar-primary__section">
        <div class="sidebar-primary-item">
<nav class="bd-docs-nav bd-links"
     aria-label="Section Navigation">
  <p class="bd-links__title" role="heading" aria-level="1">Section Navigation</p>
  <div class="bd-toc-item navbar-nav"><ul class="current nav bd-sidenav">
<li class="toctree-l1"><a class="reference internal" href="sadrive.cli.html">sadrive.cli module</a></li>
<li class="toctree-l1 current active has-children"><a class="reference internal" href="sadrive.commands.html">sadrive.commands package</a><details open="open"><summary><span class="toctree-toggle" role="presentation"><i class="fa-solid fa-chevron-down"></i></span></summary><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.config.html">sadrive.commands.config module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.db.html">sadrive.commands.db module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.delete.html">sadrive.commands.delete module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.downlaod.html">sadrive.commands.downlaod module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.manipulation.html">sadrive.commands.manipulation module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.navigate.html">sadrive.commands.navigate module</a></li>
<li class="toctree-l2 current active"><a class="current reference internal" href="#">sadrive.commands.upload module</a></li>
</ul>
</details></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.config.html">sadrive.commands.config module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.db.html">sadrive.commands.db module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.delete.html">sadrive.commands.delete module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.downlaod.html">sadrive.commands.downlaod module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.manipulation.html">sadrive.commands.manipulation module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.navigate.html">sadrive.commands.navigate module</a></li>
<li class="toctree-l1 current active"><a class="current reference internal" href="#">sadrive.commands.upload module</a></li>
<li class="toctree-l1 has-children"><a class="reference internal" href="sadrive.helpers.html">sadrive.helpers package</a><details><summary><span class="toctree-toggle" role="presentation"><i class="fa-solid fa-chevron-down"></i></span></summary><ul>
<li class="toctree-l2"><a class="reference internal" href="sadrive.helpers.dbf.html">sadrive.helpers.dbf module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.helpers.drive.html">sadrive.helpers.drive module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.helpers.utils.html">sadrive.helpers.utils module</a></li>
</ul>
</details></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.helpers.dbf.html">sadrive.helpers.dbf module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.helpers.drive.html">sadrive.helpers.drive module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.helpers.utils.html">sadrive.helpers.utils module</a></li>
</ul>
</div>
</nav></div>
    </div>
  
  
  <div class="sidebar-primary-items__end sidebar-primary__section">
      <div class="sidebar-primary-item">
<div id="ethical-ad-placement"
      class="flat"
      data-ea-publisher="readthedocs"
      data-ea-type="readthedocs-sidebar"
      data-ea-manual="true">
</div></div>
  </div>


      </div>
      
      <main id="main-content" class="bd-main" role="main">
        
        
          <div class="bd-content">
            <div class="bd-article-container">
              
              <div class="bd-header-article d-print-none">
<div class="header-article-items header-article__inner">
  
    <div class="header-article-items__start">
      
        <div class="header-article-item">

<nav aria-label="Breadcrumb" class="d-print-none">
  <ul class="bd-breadcrumbs">
    
    <li class="breadcrumb-item breadcrumb-home">
      <a href="../index.html" class="nav-link" aria-label="Home">
        <i class="fa-solid fa-home"></i>
      </a>
    </li>
    
    <li class="breadcrumb-item"><a href="../apireference.html" class="nav-link">API Reference</a></li>
    
    
    <li class="breadcrumb-item"><a href="sadrive.commands.html" class="nav-link">sadrive.commands package</a></li>
    
    <li class="breadcrumb-item active" aria-current="page"><span class="ellipsis">sadrive.commands.upload module</span></li>
  </ul>
</nav>
</div>
      
    </div>
  
  
</div>
</div>
              
              
              
                
<div id="searchbox"></div>
                <article class="bd-article">
                  
  <section id="module-sadrive.commands.upload">
<span id="sadrive-commands-upload-module"></span><h1>sadrive.commands.upload module<a class="headerlink" href="#module-sadrive.commands.upload" title="Link to this heading">#</a></h1>
<p>Handles uploading files and directories to SA-Drive, including:</p>
<ul class="simple">
<li><p>Splitting large files across multiple service accounts based on available space</p></li>
<li><p>Preparing and writing “.sapart” parts and manifest files for oversized uploads</p></li>
<li><p>Reflecting local directory structures on the remote Drive</p></li>
<li><p>Concurrent uploading with progress tracking via Rich</p></li>
<li><p>Recursive and single-file upload commands integrated with Click</p></li>
</ul>
<p>Key components:
- <cite>prepare_sapart_jobs</cite>: Divides a large file into parts and records upload jobs
- <cite>upload_file</cite>: Streams a file or part to Drive with real-time progress updates
- <cite>sem_upload_wrapper</cite>: Ensures semaphore-based concurrency control for threads
- <cite>reflect_structure_on_sadrive</cite>: Mirrors a local directory tree to remote folders
- <cite>upload</cite> command: CLI entry point orchestrating directory or file uploads</p>
<dl class="py class">
<dt class="sig sig-object py" id="sadrive.commands.upload.UploadThread">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sadrive.commands.upload.</span></span><span class="sig-name descname"><span class="pre">UploadThread</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">group</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Any</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">target</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Any</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Any</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">args</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Any</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">()</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">kwargs</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Any</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">{}</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/commands/upload.html#UploadThread"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.commands.upload.UploadThread" title="Link to this definition">#</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">Thread</span></code></p>
<p>Thread subclass that captures the return value of its target function.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sadrive.commands.upload.UploadThread._return">
<span class="sig-name descname"><span class="pre">_return</span></span><a class="headerlink" href="#sadrive.commands.upload.UploadThread._return" title="Link to this definition">#</a></dt>
<dd><p>The value returned by the target function after execution.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sadrive.commands.upload.UploadThread.run">
<span class="sig-name descname"><span class="pre">run</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/commands/upload.html#UploadThread.run"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.commands.upload.UploadThread.run" title="Link to this definition">#</a></dt>
<dd><p>Executes the target function and stores its return value.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="sadrive.commands.upload.UploadThread.join">
<span class="sig-name descname"><span class="pre">join</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timeout</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/commands/upload.html#UploadThread.join"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.commands.upload.UploadThread.join" title="Link to this definition">#</a></dt>
<dd><p>Joins the thread and returns the stored return value.</p>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id0">
<span class="sig-name descname"><span class="pre">join</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">timeout</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Any</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/commands/upload.html#UploadThread.join"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id0" title="Link to this definition">#</a></dt>
<dd><p>Waits for the thread to finish and returns the function’s return value.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>timeout</strong> – Optional timeout in seconds.</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>The return value from the target function.</p>
</dd>
</dl>
</dd></dl>

<dl class="py method">
<dt class="sig sig-object py" id="id1">
<span class="sig-name descname"><span class="pre">run</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/commands/upload.html#UploadThread.run"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#id1" title="Link to this definition">#</a></dt>
<dd><p>Runs the thread, invoking the target function and storing its result.</p>
</dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.commands.upload.prepare_sapart_jobs">
<span class="sig-prename descclassname"><span class="pre">sadrive.commands.upload.</span></span><span class="sig-name descname"><span class="pre">prepare_sapart_jobs</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file_path</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">total_size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent_folder_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">list</span><span class="p"><span class="pre">[</span></span><span class="pre">tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">int</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">str</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/sadrive/commands/upload.html#prepare_sapart_jobs"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.commands.upload.prepare_sapart_jobs" title="Link to this definition">#</a></dt>
<dd><p>Prepare split-upload jobs for a large file that exceeds single-account capacity.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>file_path</strong> – Local filesystem path to the source file.</p></li>
<li><p><strong>total_size</strong> – Total size of the source file in bytes.</p></li>
<li><p><strong>parent_folder_id</strong> – Drive folder ID where the parts will be uploaded.</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p><ul class="simple">
<li><p>part_file_path (str): Path to the generated part file.</p></li>
<li><p>part_size (int): Size of this part in bytes.</p></li>
<li><p>service_account_id (str): ID of the service account chosen for this part.</p></li>
<li><p>remote_folder_id (str): Drive folder ID for uploading this part.</p></li>
</ul>
</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>List of tuples describing each upload job. Each tuple contains</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.commands.upload.reflect_structure_on_sadrive">
<span class="sig-prename descclassname"><span class="pre">sadrive.commands.upload.</span></span><span class="sig-name descname"><span class="pre">reflect_structure_on_sadrive</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">structure</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Dict</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Dict</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">DirTree</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">int</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">int</span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">destination</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent_id_map</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">list</span><span class="p"><span class="pre">[</span></span><span class="pre">tuple</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">int</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">str</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">tmp_drive</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><a class="reference internal" href="sadrive.helpers.drive.html#sadrive.helpers.drive.SADrive" title="sadrive.helpers.drive.SADrive"><span class="pre">SADrive</span></a></span></em>, <em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">list</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">]</span></span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/commands/upload.html#reflect_structure_on_sadrive"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.commands.upload.reflect_structure_on_sadrive" title="Link to this definition">#</a></dt>
<dd><p>Recursively reflect a local directory tree structure on SA-Drive by creating folders and scheduling file uploads.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>structure</strong> – Nested dictionary mapping folder names to subtrees or file sizes.</p></li>
<li><p><strong>destination</strong> – Drive folder ID where this level’s content should be mirrored.</p></li>
<li><p><strong>parent_id_map</strong> – List tracking tuples of (folder_name, local_size, drive_folder_id) for created folders.</p></li>
<li><p><strong>tmp_drive</strong> – Authenticated SADrive instance used for folder creation.</p></li>
<li><p><strong>path</strong> – Accumulated list of path segments representing the current traversal.</p></li>
</ul>
</dd>
</dl>
<dl class="simple">
<dt>Behavior:</dt><dd><ul class="simple">
<li><p>Iterates through the <cite>structure</cite> dict:
- For sub-dictionaries, creates a corresponding folder on Drive, updates <cite>parent_id_map</cite>, and recurses.
- For file entries (size values), schedules upload jobs for each file part.</p></li>
<li><p>Does not perform actual uploads; integrates with the main <cite>upload</cite> command logic.</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.commands.upload.sem_upload_wrapper">
<span class="sig-prename descclassname"><span class="pre">sadrive.commands.upload.</span></span><span class="sig-name descname"><span class="pre">sem_upload_wrapper</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">fp</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sz</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">pid</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">progress</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Progress</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">task_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sa</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/commands/upload.html#sem_upload_wrapper"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.commands.upload.sem_upload_wrapper" title="Link to this definition">#</a></dt>
<dd><p>Wrapper for uploading a file that ensures semaphore release after completion.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>fp</strong> – Local file path to upload.</p></li>
<li><p><strong>sz</strong> – File size in bytes.</p></li>
<li><p><strong>pid</strong> – Drive parent folder ID.</p></li>
<li><p><strong>progress</strong> – Rich Progress instance for tracking upload progress.</p></li>
<li><p><strong>task_id</strong> – Identifier for the progress task.</p></li>
<li><p><strong>sa</strong> – Service account ID to use for this upload.</p></li>
</ul>
</dd>
</dl>
<dl class="simple">
<dt>Behavior:</dt><dd><ul class="simple">
<li><p>Calls <cite>upload_file</cite> with provided arguments.</p></li>
<li><p>Ensures the global <cite>sem</cite> is released even if an error occurs.</p></li>
</ul>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.commands.upload.upload_file">
<span class="sig-prename descclassname"><span class="pre">sadrive.commands.upload.</span></span><span class="sig-name descname"><span class="pre">upload_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file_path</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent_folder_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">progress</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Progress</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">task_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">sa_num_provided</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">''</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Dict</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">int</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/sadrive/commands/upload.html#upload_file"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.commands.upload.upload_file" title="Link to this definition">#</a></dt>
<dd><p>Upload a file or part to SA-Drive, streaming with progress tracking.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>file_path</strong> – Local path to the file or part to upload.</p></li>
<li><p><strong>size</strong> – Total size of the upload in bytes.</p></li>
<li><p><strong>parent_folder_id</strong> – Drive folder ID where the file will be stored.</p></li>
<li><p><strong>progress</strong> – Rich Progress instance for updating the progress bar.</p></li>
<li><p><strong>task_id</strong> – Task identifier for Rich progress updates.</p></li>
<li><p><strong>sa_num_provided</strong> – Optional service account ID to use; if empty, choose automatically.</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Dict containing details of the uploaded file from the Drive API response,
including ‘id’, ‘title’, ‘parents’, and ‘fileSize’.</p>
</dd>
</dl>
</dd></dl>

</section>


                </article>
              
              
              
              
              
                <footer class="prev-next-footer d-print-none">
                  
<div class="prev-next-area">
    <a class="left-prev"
       href="sadrive.commands.navigate.html"
       title="previous page">
      <i class="fa-solid fa-angle-left"></i>
      <div class="prev-next-info">
        <p class="prev-next-subtitle">previous</p>
        <p class="prev-next-title">sadrive.commands.navigate module</p>
      </div>
    </a>
    <a class="right-next"
       href="sadrive.helpers.html"
       title="next page">
      <div class="prev-next-info">
        <p class="prev-next-subtitle">next</p>
        <p class="prev-next-title">sadrive.helpers package</p>
      </div>
      <i class="fa-solid fa-angle-right"></i>
    </a>
</div>
                </footer>
              
            </div>
            
            
              
                <dialog id="pst-secondary-sidebar-modal"></dialog>
                <div id="pst-secondary-sidebar" class="bd-sidebar-secondary bd-toc"><div class="sidebar-secondary-items sidebar-secondary__inner">


  <div class="sidebar-secondary-item">
<div
    id="pst-page-navigation-heading-2"
    class="page-toc tocsection onthispage">
    <i class="fa-solid fa-list"></i> On this page
  </div>
  <nav class="bd-toc-nav page-toc" aria-labelledby="pst-page-navigation-heading-2">
    <ul class="visible nav section-nav flex-column">
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.commands.upload.UploadThread"><code class="docutils literal notranslate"><span class="pre">UploadThread</span></code></a><ul class="nav section-nav flex-column">
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.commands.upload.UploadThread._return"><code class="docutils literal notranslate"><span class="pre">UploadThread._return</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.commands.upload.UploadThread.run"><code class="docutils literal notranslate"><span class="pre">UploadThread.run()</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.commands.upload.UploadThread.join"><code class="docutils literal notranslate"><span class="pre">UploadThread.join()</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#id0"><code class="docutils literal notranslate"><span class="pre">UploadThread.join()</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#id1"><code class="docutils literal notranslate"><span class="pre">UploadThread.run()</span></code></a></li>
</ul>
</li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.commands.upload.prepare_sapart_jobs"><code class="docutils literal notranslate"><span class="pre">prepare_sapart_jobs()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.commands.upload.reflect_structure_on_sadrive"><code class="docutils literal notranslate"><span class="pre">reflect_structure_on_sadrive()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.commands.upload.sem_upload_wrapper"><code class="docutils literal notranslate"><span class="pre">sem_upload_wrapper()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.commands.upload.upload_file"><code class="docutils literal notranslate"><span class="pre">upload_file()</span></code></a></li>
</ul>
  </nav></div>

  <div class="sidebar-secondary-item">
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/modules/sadrive.commands.upload.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div></div>

</div></div>
              
            
          </div>
          <footer class="bd-footer-content">
            
          </footer>
        
      </main>
    </div>
  </div>
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script defer src="../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf"></script>
<script defer src="../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf"></script>

  <footer class="bd-footer">
<div class="bd-footer__inner bd-page-width">
  
    <div class="footer-items__start">
      
        <div class="footer-item">

  <p class="copyright">
    
      © Copyright 2025, jsmaskeen.
      <br/>
    
  </p>
</div>
      
        <div class="footer-item">

  <p class="sphinx-version">
    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    <br/>
  </p>
</div>
      
    </div>
  
  
  
    <div class="footer-items__end">
      
        <div class="footer-item">
<p class="theme-version">
  <!-- # L10n: Setting the PST URL as an argument as this does not need to be localized -->
  Built with the <a href="https://pydata-sphinx-theme.readthedocs.io/en/stable/index.html">PyData Sphinx Theme</a> 0.16.1.
</p></div>
      
    </div>
  
</div>

  </footer>
  </body>
</html>