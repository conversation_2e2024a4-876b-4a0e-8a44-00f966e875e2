
<!DOCTYPE html>


<html lang="en" data-content_root="../" >

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>sadrive.helpers.utils module &#8212; sadrive 2.0.0 documentation</title>
  
  
  
  <script data-cfasync="false">
    document.documentElement.dataset.mode = localStorage.getItem("mode") || "";
    document.documentElement.dataset.theme = localStorage.getItem("theme") || "";
  </script>
  <!--
    this give us a css class that will be invisible only if js is disabled
  -->
  <noscript>
    <style>
      .pst-js-only { display: none !important; }

    </style>
  </noscript>
  
  <!-- Loaded before other Sphinx assets -->
  <link href="../_static/styles/theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />
<link href="../_static/styles/pydata-sphinx-theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />

    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=8f2a1f02" />
  
  <!-- So that users can add custom icons -->
  <script src="../_static/scripts/fontawesome.js?digest=8878045cc6db502f8baf"></script>
  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf" />
<link rel="preload" as="script" href="../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf" />

    <script src="../_static/documentation_options.js?v=51b770b3"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script>DOCUMENTATION_OPTIONS.pagename = 'modules/sadrive.helpers.utils';</script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="prev" title="sadrive.helpers.drive module" href="sadrive.helpers.drive.html" />
  <meta name="viewport" content="width=device-width, initial-scale=1"/>
  <meta name="docsearch:language" content="en"/>
  <meta name="docsearch:version" content="" />
  </head>
  
  
  <body data-bs-spy="scroll" data-bs-target=".bd-toc-nav" data-offset="180" data-bs-root-margin="0px 0px -60%" data-default-mode="">

  
  
  <div id="pst-skip-link" class="skip-link d-print-none"><a href="#main-content">Skip to main content</a></div>
  
  <div id="pst-scroll-pixel-helper"></div>
  
  <button type="button" class="btn rounded-pill" id="pst-back-to-top">
    <i class="fa-solid fa-arrow-up"></i>Back to top</button>

  
  <dialog id="pst-search-dialog">
    
<form class="bd-search d-flex align-items-center"
      action="../search.html"
      method="get">
  <i class="fa-solid fa-magnifying-glass"></i>
  <input type="search"
         class="form-control"
         name="q"
         placeholder="Search the docs ..."
         aria-label="Search the docs ..."
         autocomplete="off"
         autocorrect="off"
         autocapitalize="off"
         spellcheck="false"/>
  <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd>K</kbd></span>
</form>
  </dialog>

  <div class="pst-async-banner-revealer d-none">
  <aside id="bd-header-version-warning" class="d-none d-print-none" aria-label="Version warning"></aside>
</div>

  
    <header class="bd-header navbar navbar-expand-lg bd-navbar d-print-none">
<div class="bd-header__inner bd-page-width">
  <button class="pst-navbar-icon sidebar-toggle primary-toggle" aria-label="Site navigation">
    <span class="fa-solid fa-bars"></span>
  </button>
  
  
  <div class="col-lg-3 navbar-header-items__start">
    
      <div class="navbar-item">

  
    
  

<a class="navbar-brand logo" href="../index.html">
  
  
  
  
  
  
    <p class="title logo__title">sadrive 2.0.0 documentation</p>
  
</a></div>
    
  </div>
  
  <div class="col-lg-9 navbar-header-items">
    
    <div class="me-auto navbar-header-items__center">
      
        <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item current active">
  <a class="nav-link nav-internal" href="../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
      
    </div>
    
    
    <div class="navbar-header-items__end">
      
        <div class="navbar-item navbar-persistent--container">
          

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
        </div>
      
      
        <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
      
    </div>
    
  </div>
  
  
    <div class="navbar-persistent--mobile">

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
    </div>
  

  
    <button class="pst-navbar-icon sidebar-toggle secondary-toggle" aria-label="On this page">
      <span class="fa-solid fa-outdent"></span>
    </button>
  
</div>

    </header>
  

  <div class="bd-container">
    <div class="bd-container__inner bd-page-width">
      
      
      
      <dialog id="pst-primary-sidebar-modal"></dialog>
      <div id="pst-primary-sidebar" class="bd-sidebar-primary bd-sidebar">
        

  
  <div class="sidebar-header-items sidebar-primary__section">
    
    
      <div class="sidebar-header-items__center">
        
          
          
            <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item current active">
  <a class="nav-link nav-internal" href="../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
          
        
      </div>
    
    
    
      <div class="sidebar-header-items__end">
        
          <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
        
      </div>
    
  </div>
  
    <div class="sidebar-primary-items__start sidebar-primary__section">
        <div class="sidebar-primary-item">
<nav class="bd-docs-nav bd-links"
     aria-label="Section Navigation">
  <p class="bd-links__title" role="heading" aria-level="1">Section Navigation</p>
  <div class="bd-toc-item navbar-nav"><ul class="current nav bd-sidenav">
<li class="toctree-l1"><a class="reference internal" href="sadrive.cli.html">sadrive.cli module</a></li>
<li class="toctree-l1 has-children"><a class="reference internal" href="sadrive.commands.html">sadrive.commands package</a><details><summary><span class="toctree-toggle" role="presentation"><i class="fa-solid fa-chevron-down"></i></span></summary><ul>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.config.html">sadrive.commands.config module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.db.html">sadrive.commands.db module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.delete.html">sadrive.commands.delete module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.downlaod.html">sadrive.commands.downlaod module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.manipulation.html">sadrive.commands.manipulation module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.navigate.html">sadrive.commands.navigate module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.commands.upload.html">sadrive.commands.upload module</a></li>
</ul>
</details></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.config.html">sadrive.commands.config module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.db.html">sadrive.commands.db module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.delete.html">sadrive.commands.delete module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.downlaod.html">sadrive.commands.downlaod module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.manipulation.html">sadrive.commands.manipulation module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.navigate.html">sadrive.commands.navigate module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.commands.upload.html">sadrive.commands.upload module</a></li>
<li class="toctree-l1 current active has-children"><a class="reference internal" href="sadrive.helpers.html">sadrive.helpers package</a><details open="open"><summary><span class="toctree-toggle" role="presentation"><i class="fa-solid fa-chevron-down"></i></span></summary><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="sadrive.helpers.dbf.html">sadrive.helpers.dbf module</a></li>
<li class="toctree-l2"><a class="reference internal" href="sadrive.helpers.drive.html">sadrive.helpers.drive module</a></li>
<li class="toctree-l2 current active"><a class="current reference internal" href="#">sadrive.helpers.utils module</a></li>
</ul>
</details></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.helpers.dbf.html">sadrive.helpers.dbf module</a></li>
<li class="toctree-l1"><a class="reference internal" href="sadrive.helpers.drive.html">sadrive.helpers.drive module</a></li>
<li class="toctree-l1 current active"><a class="current reference internal" href="#">sadrive.helpers.utils module</a></li>
</ul>
</div>
</nav></div>
    </div>
  
  
  <div class="sidebar-primary-items__end sidebar-primary__section">
      <div class="sidebar-primary-item">
<div id="ethical-ad-placement"
      class="flat"
      data-ea-publisher="readthedocs"
      data-ea-type="readthedocs-sidebar"
      data-ea-manual="true">
</div></div>
  </div>


      </div>
      
      <main id="main-content" class="bd-main" role="main">
        
        
          <div class="bd-content">
            <div class="bd-article-container">
              
              <div class="bd-header-article d-print-none">
<div class="header-article-items header-article__inner">
  
    <div class="header-article-items__start">
      
        <div class="header-article-item">

<nav aria-label="Breadcrumb" class="d-print-none">
  <ul class="bd-breadcrumbs">
    
    <li class="breadcrumb-item breadcrumb-home">
      <a href="../index.html" class="nav-link" aria-label="Home">
        <i class="fa-solid fa-home"></i>
      </a>
    </li>
    
    <li class="breadcrumb-item"><a href="../apireference.html" class="nav-link">API Reference</a></li>
    
    
    <li class="breadcrumb-item"><a href="sadrive.helpers.html" class="nav-link">sadrive.helpers package</a></li>
    
    <li class="breadcrumb-item active" aria-current="page"><span class="ellipsis">sadrive.helpers.utils module</span></li>
  </ul>
</nav>
</div>
      
    </div>
  
  
</div>
</div>
              
              
              
                
<div id="searchbox"></div>
                <article class="bd-article">
                  
  <section id="module-sadrive.helpers.utils">
<span id="sadrive-helpers-utils-module"></span><h1>sadrive.helpers.utils module<a class="headerlink" href="#module-sadrive.helpers.utils" title="Link to this heading">#</a></h1>
<p>This module provides helper functions and utilities for the CLI application.
It uses service account storage for Google Drive operations and offers:</p>
<ul class="simple">
<li><p>Configuration directory handling</p></li>
<li><p>Path construction for accounts and database</p></li>
<li><p>Rclone configuration initialization</p></li>
<li><p>Human-readable byte and time formatting</p></li>
<li><p>List partitioning</p></li>
<li><p>Generator wrapper</p></li>
<li><p>Service account selection by free space</p></li>
<li><p>Directory tree mapping</p></li>
<li><p>File size measurement</p></li>
<li><p>Filename shortening</p></li>
</ul>
<p>Constants:
- CONFIG_POINTER: Path to the file storing the config directory pointer
- MAGIC_SIZE: Total capacity threshold for service accounts (in bytes)
- BUFFER: Buffer size threshold (in bytes)
- MAX_THREADS: Maximum number of threads permitted</p>
<dl class="py class">
<dt class="sig sig-object py" id="sadrive.helpers.utils.FF">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">FF</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">parent_id</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">type</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#FF"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.FF" title="Link to this definition">#</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Represents a file or folder entry in Google Drive.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sadrive.helpers.utils.FF.name">
<span class="sig-name descname"><span class="pre">name</span></span><a class="headerlink" href="#sadrive.helpers.utils.FF.name" title="Link to this definition">#</a></dt>
<dd><p>The display name of the file or folder.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sadrive.helpers.utils.FF.file_id">
<span class="sig-name descname"><span class="pre">file_id</span></span><a class="headerlink" href="#sadrive.helpers.utils.FF.file_id" title="Link to this definition">#</a></dt>
<dd><p>The unique identifier in Drive.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sadrive.helpers.utils.FF.parent_id">
<span class="sig-name descname"><span class="pre">parent_id</span></span><a class="headerlink" href="#sadrive.helpers.utils.FF.parent_id" title="Link to this definition">#</a></dt>
<dd><p>The parent folder’s identifier.</p>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sadrive.helpers.utils.FF.type">
<span class="sig-name descname"><span class="pre">type</span></span><a class="headerlink" href="#sadrive.helpers.utils.FF.type" title="Link to this definition">#</a></dt>
<dd><p>The entry type, e.g. ‘folder’ or ‘file’.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sadrive.helpers.utils.Generator">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">Generator</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">gen</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Any</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#Generator"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.Generator" title="Link to this definition">#</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Wrapper to enable ‘yield from’ for a generator function.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sadrive.helpers.utils.Generator.gen">
<span class="sig-name descname"><span class="pre">gen</span></span><a class="headerlink" href="#sadrive.helpers.utils.Generator.gen" title="Link to this definition">#</a></dt>
<dd><p>The underlying generator.</p>
</dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sadrive.helpers.utils.Manifest">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">Manifest</span></span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#Manifest"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.Manifest" title="Link to this definition">#</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TypedDict</span></code></p>
<p>Manifest describing an original file and its parts.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sadrive.helpers.utils.Manifest.original_filename">
<span class="sig-name descname"><span class="pre">original_filename</span></span><a class="headerlink" href="#sadrive.helpers.utils.Manifest.original_filename" title="Link to this definition">#</a></dt>
<dd><p>The original filename before splitting.</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>str</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sadrive.helpers.utils.Manifest.parts">
<span class="sig-name descname"><span class="pre">parts</span></span><a class="headerlink" href="#sadrive.helpers.utils.Manifest.parts" title="Link to this definition">#</a></dt>
<dd><p>A list of PartInfo dictionaries for each part.</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>List[<a class="reference internal" href="#sadrive.helpers.utils.PartInfo" title="sadrive.helpers.utils.PartInfo">sadrive.helpers.utils.PartInfo</a>]</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id0">
<span class="sig-name descname"><span class="pre">original_filename</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#id0" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id1">
<span class="sig-name descname"><span class="pre">parts</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><a class="reference internal" href="#sadrive.helpers.utils.PartInfo" title="sadrive.helpers.utils.PartInfo"><span class="pre">PartInfo</span></a><span class="p"><span class="pre">]</span></span></em><a class="headerlink" href="#id1" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py class">
<dt class="sig sig-object py" id="sadrive.helpers.utils.PartInfo">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">PartInfo</span></span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#PartInfo"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.PartInfo" title="Link to this definition">#</a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">TypedDict</span></code></p>
<p>Information about a part of a split file.</p>
<dl class="py attribute">
<dt class="sig sig-object py" id="sadrive.helpers.utils.PartInfo.filename">
<span class="sig-name descname"><span class="pre">filename</span></span><a class="headerlink" href="#sadrive.helpers.utils.PartInfo.filename" title="Link to this definition">#</a></dt>
<dd><p>Name of the file part.</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>str</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="sadrive.helpers.utils.PartInfo.size">
<span class="sig-name descname"><span class="pre">size</span></span><a class="headerlink" href="#sadrive.helpers.utils.PartInfo.size" title="Link to this definition">#</a></dt>
<dd><p>Size of the file part in bytes.</p>
<dl class="field-list simple">
<dt class="field-odd">Type<span class="colon">:</span></dt>
<dd class="field-odd"><p>int</p>
</dd>
</dl>
</dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id2">
<span class="sig-name descname"><span class="pre">filename</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">str</span></em><a class="headerlink" href="#id2" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py attribute">
<dt class="sig sig-object py" id="id3">
<span class="sig-name descname"><span class="pre">size</span></span><em class="property"><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="pre">int</span></em><a class="headerlink" href="#id3" title="Link to this definition">#</a></dt>
<dd></dd></dl>

</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.utils.get_accounts_path">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">get_accounts_path</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Path</span></span></span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#get_accounts_path"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.get_accounts_path" title="Link to this definition">#</a></dt>
<dd><p>Constructs the path to the ‘accounts’ subdirectory within the config directory.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Path to the service accounts directory.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>Path</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.utils.get_config_dir">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">get_config_dir</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#sadrive.helpers.utils.get_config_dir" title="Link to this definition">#</a></dt>
<dd></dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.utils.get_database_path">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">get_database_path</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Path</span></span></span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#get_database_path"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.get_database_path" title="Link to this definition">#</a></dt>
<dd><p>Constructs the path to the SQLite database file within the config directory.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Path to the ‘database.db’ file.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>Path</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.utils.get_dir_structure">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">get_dir_structure</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">path</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Path</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Dict</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Dict</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">DirTree</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">int</span><span class="p"><span class="pre">]</span></span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">int</span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#get_dir_structure"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.get_dir_structure" title="Link to this definition">#</a></dt>
<dd><p>Recursively builds a directory tree mapping folder names to subtrees or file sizes.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>path</strong> – Root directory path.</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Nested dict mapping names to file sizes or further DirTrees.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>DirTree</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.utils.get_file_size">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">get_file_size</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">file_path</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">Path</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#get_file_size"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.get_file_size" title="Link to this definition">#</a></dt>
<dd><p>Returns the size of a file in bytes by seeking to its end.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>file_path</strong> – Path to the target file.</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>File size in bytes.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>int</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.utils.get_free_sa">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">get_free_sa</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">sa_map</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">List</span><span class="p"><span class="pre">[</span></span><span class="pre">dict</span><span class="p"><span class="pre">[</span></span><span class="pre">str</span><span class="p"><span class="pre">,</span></span><span class="w"> </span><span class="pre">Any</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">file_size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#get_free_sa"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.get_free_sa" title="Link to this definition">#</a></dt>
<dd><p>Selects service account IDs with enough free space.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>sa_map</strong> – List of dicts containing ‘_id’ and ‘size’ keys.</p></li>
<li><p><strong>file_size</strong> – Required file size in bytes.</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Sorted list of account IDs that can accommodate the file.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>List[int]</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.utils.get_gclone_exe">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">get_gclone_exe</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">Path</span></span></span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#get_gclone_exe"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.get_gclone_exe" title="Link to this definition">#</a></dt>
<dd><p>Reads and returns the path to the gclone executable from config.json.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>Path to the ‘gclone’ executable.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>Path</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.utils.get_parent_id">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">get_parent_id</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#get_parent_id"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.get_parent_id" title="Link to this definition">#</a></dt>
<dd><p>Reads and returns the parent folder ID from config.json.</p>
<dl class="field-list simple">
<dt class="field-odd">Returns<span class="colon">:</span></dt>
<dd class="field-odd"><p>The ‘parent_id’ value stored in config.json.</p>
</dd>
<dt class="field-even">Return type<span class="colon">:</span></dt>
<dd class="field-even"><p>str</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.utils.humanbytes">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">humanbytes</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">size</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">float</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#humanbytes"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.humanbytes" title="Link to this definition">#</a></dt>
<dd><p>Converts a size in bytes to a human-readable string.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>size</strong> – Size in bytes.</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Formatted size (e.g. ‘1.234 MiB’).</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>str</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.utils.humantime">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">humantime</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">seconds</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#humantime"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.humantime" title="Link to this definition">#</a></dt>
<dd><p>Formats a duration in seconds into HhMmSs or MmSs.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><p><strong>seconds</strong> – Duration in seconds.</p>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Formatted time string.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>str</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.utils.list_into_n_parts">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">list_into_n_parts</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">lst</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">List</span><span class="p"><span class="pre">[</span></span><span class="pre">T</span><span class="p"><span class="pre">]</span></span></span></em>, <em class="sig-param"><span class="n"><span class="pre">n</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">List</span><span class="p"><span class="pre">[</span></span><span class="pre">List</span><span class="p"><span class="pre">[</span></span><span class="pre">T</span><span class="p"><span class="pre">]</span></span><span class="p"><span class="pre">]</span></span></span></span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#list_into_n_parts"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.list_into_n_parts" title="Link to this definition">#</a></dt>
<dd><p>Splits a list into n approximately equal parts.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>lst</strong> – List of items to split.</p></li>
<li><p><strong>n</strong> – Number of parts.</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>A list containing n sublists.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>List[List[T]]</p>
</dd>
</dl>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.utils.set_rclone_conf">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">set_rclone_conf</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#set_rclone_conf"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.set_rclone_conf" title="Link to this definition">#</a></dt>
<dd><p>Creates rcone.conf next to gclone executable with default content using the first service account.</p>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="sadrive.helpers.utils.shorten_fn">
<span class="sig-prename descclassname"><span class="pre">sadrive.helpers.utils.</span></span><span class="sig-name descname"><span class="pre">shorten_fn</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">name</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">max_len</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">75</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">str</span></span></span><a class="reference internal" href="../_modules/sadrive/helpers/utils.html#shorten_fn"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#sadrive.helpers.utils.shorten_fn" title="Link to this definition">#</a></dt>
<dd><p>Truncates a filename to a maximum length with an ellipsis in the middle.</p>
<dl class="field-list simple">
<dt class="field-odd">Parameters<span class="colon">:</span></dt>
<dd class="field-odd"><ul class="simple">
<li><p><strong>name</strong> – Original filename.</p></li>
<li><p><strong>max_len</strong> – Maximum allowed length.</p></li>
</ul>
</dd>
<dt class="field-even">Returns<span class="colon">:</span></dt>
<dd class="field-even"><p>Shortened filename if needed, else original.</p>
</dd>
<dt class="field-odd">Return type<span class="colon">:</span></dt>
<dd class="field-odd"><p>str</p>
</dd>
</dl>
</dd></dl>

</section>


                </article>
              
              
              
              
              
                <footer class="prev-next-footer d-print-none">
                  
<div class="prev-next-area">
    <a class="left-prev"
       href="sadrive.helpers.drive.html"
       title="previous page">
      <i class="fa-solid fa-angle-left"></i>
      <div class="prev-next-info">
        <p class="prev-next-subtitle">previous</p>
        <p class="prev-next-title">sadrive.helpers.drive module</p>
      </div>
    </a>
</div>
                </footer>
              
            </div>
            
            
              
                <dialog id="pst-secondary-sidebar-modal"></dialog>
                <div id="pst-secondary-sidebar" class="bd-sidebar-secondary bd-toc"><div class="sidebar-secondary-items sidebar-secondary__inner">


  <div class="sidebar-secondary-item">
<div
    id="pst-page-navigation-heading-2"
    class="page-toc tocsection onthispage">
    <i class="fa-solid fa-list"></i> On this page
  </div>
  <nav class="bd-toc-nav page-toc" aria-labelledby="pst-page-navigation-heading-2">
    <ul class="visible nav section-nav flex-column">
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.FF"><code class="docutils literal notranslate"><span class="pre">FF</span></code></a><ul class="nav section-nav flex-column">
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.FF.name"><code class="docutils literal notranslate"><span class="pre">FF.name</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.FF.file_id"><code class="docutils literal notranslate"><span class="pre">FF.file_id</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.FF.parent_id"><code class="docutils literal notranslate"><span class="pre">FF.parent_id</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.FF.type"><code class="docutils literal notranslate"><span class="pre">FF.type</span></code></a></li>
</ul>
</li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.Generator"><code class="docutils literal notranslate"><span class="pre">Generator</span></code></a><ul class="nav section-nav flex-column">
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.Generator.gen"><code class="docutils literal notranslate"><span class="pre">Generator.gen</span></code></a></li>
</ul>
</li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.Manifest"><code class="docutils literal notranslate"><span class="pre">Manifest</span></code></a><ul class="nav section-nav flex-column">
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.Manifest.original_filename"><code class="docutils literal notranslate"><span class="pre">Manifest.original_filename</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.Manifest.parts"><code class="docutils literal notranslate"><span class="pre">Manifest.parts</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#id0"><code class="docutils literal notranslate"><span class="pre">Manifest.original_filename</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#id1"><code class="docutils literal notranslate"><span class="pre">Manifest.parts</span></code></a></li>
</ul>
</li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.PartInfo"><code class="docutils literal notranslate"><span class="pre">PartInfo</span></code></a><ul class="nav section-nav flex-column">
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.PartInfo.filename"><code class="docutils literal notranslate"><span class="pre">PartInfo.filename</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.PartInfo.size"><code class="docutils literal notranslate"><span class="pre">PartInfo.size</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#id2"><code class="docutils literal notranslate"><span class="pre">PartInfo.filename</span></code></a></li>
<li class="toc-h3 nav-item toc-entry"><a class="reference internal nav-link" href="#id3"><code class="docutils literal notranslate"><span class="pre">PartInfo.size</span></code></a></li>
</ul>
</li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.get_accounts_path"><code class="docutils literal notranslate"><span class="pre">get_accounts_path()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.get_config_dir"><code class="docutils literal notranslate"><span class="pre">get_config_dir()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.get_database_path"><code class="docutils literal notranslate"><span class="pre">get_database_path()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.get_dir_structure"><code class="docutils literal notranslate"><span class="pre">get_dir_structure()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.get_file_size"><code class="docutils literal notranslate"><span class="pre">get_file_size()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.get_free_sa"><code class="docutils literal notranslate"><span class="pre">get_free_sa()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.get_gclone_exe"><code class="docutils literal notranslate"><span class="pre">get_gclone_exe()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.get_parent_id"><code class="docutils literal notranslate"><span class="pre">get_parent_id()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.humanbytes"><code class="docutils literal notranslate"><span class="pre">humanbytes()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.humantime"><code class="docutils literal notranslate"><span class="pre">humantime()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.list_into_n_parts"><code class="docutils literal notranslate"><span class="pre">list_into_n_parts()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.set_rclone_conf"><code class="docutils literal notranslate"><span class="pre">set_rclone_conf()</span></code></a></li>
<li class="toc-h2 nav-item toc-entry"><a class="reference internal nav-link" href="#sadrive.helpers.utils.shorten_fn"><code class="docutils literal notranslate"><span class="pre">shorten_fn()</span></code></a></li>
</ul>
  </nav></div>

  <div class="sidebar-secondary-item">
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/modules/sadrive.helpers.utils.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div></div>

</div></div>
              
            
          </div>
          <footer class="bd-footer-content">
            
          </footer>
        
      </main>
    </div>
  </div>
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script defer src="../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf"></script>
<script defer src="../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf"></script>

  <footer class="bd-footer">
<div class="bd-footer__inner bd-page-width">
  
    <div class="footer-items__start">
      
        <div class="footer-item">

  <p class="copyright">
    
      © Copyright 2025, jsmaskeen.
      <br/>
    
  </p>
</div>
      
        <div class="footer-item">

  <p class="sphinx-version">
    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    <br/>
  </p>
</div>
      
    </div>
  
  
  
    <div class="footer-items__end">
      
        <div class="footer-item">
<p class="theme-version">
  <!-- # L10n: Setting the PST URL as an argument as this does not need to be localized -->
  Built with the <a href="https://pydata-sphinx-theme.readthedocs.io/en/stable/index.html">PyData Sphinx Theme</a> 0.16.1.
</p></div>
      
    </div>
  
</div>

  </footer>
  </body>
</html>