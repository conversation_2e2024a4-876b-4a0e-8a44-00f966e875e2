
<!DOCTYPE html>


<html lang="en" data-content_root="../../../" >

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>sadrive.commands.upload &#8212; sadrive 2.0.0 documentation</title>
  
  
  
  <script data-cfasync="false">
    document.documentElement.dataset.mode = localStorage.getItem("mode") || "";
    document.documentElement.dataset.theme = localStorage.getItem("theme") || "";
  </script>
  <!--
    this give us a css class that will be invisible only if js is disabled
  -->
  <noscript>
    <style>
      .pst-js-only { display: none !important; }

    </style>
  </noscript>
  
  <!-- Loaded before other Sphinx assets -->
  <link href="../../../_static/styles/theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />
<link href="../../../_static/styles/pydata-sphinx-theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />

    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=8f2a1f02" />
  
  <!-- So that users can add custom icons -->
  <script src="../../../_static/scripts/fontawesome.js?digest=8878045cc6db502f8baf"></script>
  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="../../../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf" />
<link rel="preload" as="script" href="../../../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf" />

    <script src="../../../_static/documentation_options.js?v=51b770b3"></script>
    <script src="../../../_static/doctools.js?v=9bcbadda"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script>DOCUMENTATION_OPTIONS.pagename = '_modules/sadrive/commands/upload';</script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
  <meta name="viewport" content="width=device-width, initial-scale=1"/>
  <meta name="docsearch:language" content="en"/>
  <meta name="docsearch:version" content="" />
  </head>
  
  
  <body data-bs-spy="scroll" data-bs-target=".bd-toc-nav" data-offset="180" data-bs-root-margin="0px 0px -60%" data-default-mode="">

  
  
  <div id="pst-skip-link" class="skip-link d-print-none"><a href="#main-content">Skip to main content</a></div>
  
  <div id="pst-scroll-pixel-helper"></div>
  
  <button type="button" class="btn rounded-pill" id="pst-back-to-top">
    <i class="fa-solid fa-arrow-up"></i>Back to top</button>

  
  <dialog id="pst-search-dialog">
    
<form class="bd-search d-flex align-items-center"
      action="../../../search.html"
      method="get">
  <i class="fa-solid fa-magnifying-glass"></i>
  <input type="search"
         class="form-control"
         name="q"
         placeholder="Search the docs ..."
         aria-label="Search the docs ..."
         autocomplete="off"
         autocorrect="off"
         autocapitalize="off"
         spellcheck="false"/>
  <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd>K</kbd></span>
</form>
  </dialog>

  <div class="pst-async-banner-revealer d-none">
  <aside id="bd-header-version-warning" class="d-none d-print-none" aria-label="Version warning"></aside>
</div>

  
    <header class="bd-header navbar navbar-expand-lg bd-navbar d-print-none">
<div class="bd-header__inner bd-page-width">
  <button class="pst-navbar-icon sidebar-toggle primary-toggle" aria-label="Site navigation">
    <span class="fa-solid fa-bars"></span>
  </button>
  
  
  <div class="col-lg-3 navbar-header-items__start">
    
      <div class="navbar-item">

  
    
  

<a class="navbar-brand logo" href="../../../index.html">
  
  
  
  
  
  
    <p class="title logo__title">sadrive 2.0.0 documentation</p>
  
</a></div>
    
  </div>
  
  <div class="col-lg-9 navbar-header-items">
    
    <div class="me-auto navbar-header-items__center">
      
        <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
      
    </div>
    
    
    <div class="navbar-header-items__end">
      
        <div class="navbar-item navbar-persistent--container">
          

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
        </div>
      
      
        <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
      
    </div>
    
  </div>
  
  
    <div class="navbar-persistent--mobile">

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
    </div>
  

  
</div>

    </header>
  

  <div class="bd-container">
    <div class="bd-container__inner bd-page-width">
      
      
      
        
      
      <dialog id="pst-primary-sidebar-modal"></dialog>
      <div id="pst-primary-sidebar" class="bd-sidebar-primary bd-sidebar hide-on-wide">
        

  
  <div class="sidebar-header-items sidebar-primary__section">
    
    
      <div class="sidebar-header-items__center">
        
          
          
            <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
          
        
      </div>
    
    
    
      <div class="sidebar-header-items__end">
        
          <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
        
      </div>
    
  </div>
  
  
  <div class="sidebar-primary-items__end sidebar-primary__section">
      <div class="sidebar-primary-item">
<div id="ethical-ad-placement"
      class="flat"
      data-ea-publisher="readthedocs"
      data-ea-type="readthedocs-sidebar"
      data-ea-manual="true">
</div></div>
  </div>


      </div>
      
      <main id="main-content" class="bd-main" role="main">
        
        
          <div class="bd-content">
            <div class="bd-article-container">
              
              <div class="bd-header-article d-print-none">
<div class="header-article-items header-article__inner">
  
    <div class="header-article-items__start">
      
        <div class="header-article-item">

<nav aria-label="Breadcrumb" class="d-print-none">
  <ul class="bd-breadcrumbs">
    
    <li class="breadcrumb-item breadcrumb-home">
      <a href="../../../index.html" class="nav-link" aria-label="Home">
        <i class="fa-solid fa-home"></i>
      </a>
    </li>
    
    <li class="breadcrumb-item"><a href="../../index.html" class="nav-link">Module code</a></li>
    
    <li class="breadcrumb-item active" aria-current="page"><span class="ellipsis">sadrive.commands.upload</span></li>
  </ul>
</nav>
</div>
      
    </div>
  
  
</div>
</div>
              
              
              
                
<div id="searchbox"></div>
                <article class="bd-article">
                  
  <h1>Source code for sadrive.commands.upload</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">Handles uploading files and directories to SA-Drive, including:</span>

<span class="sd">- Splitting large files across multiple service accounts based on available space</span>
<span class="sd">- Preparing and writing &quot;.sapart&quot; parts and manifest files for oversized uploads</span>
<span class="sd">- Reflecting local directory structures on the remote Drive</span>
<span class="sd">- Concurrent uploading with progress tracking via Rich</span>
<span class="sd">- Recursive and single-file upload commands integrated with Click</span>

<span class="sd">Key components:</span>
<span class="sd">- `prepare_sapart_jobs`: Divides a large file into parts and records upload jobs</span>
<span class="sd">- `upload_file`: Streams a file or part to Drive with real-time progress updates</span>
<span class="sd">- `sem_upload_wrapper`: Ensures semaphore-based concurrency control for threads</span>
<span class="sd">- `reflect_structure_on_sadrive`: Mirrors a local directory tree to remote folders</span>
<span class="sd">- `upload` command: CLI entry point orchestrating directory or file uploads</span>
<span class="sd">&quot;&quot;&quot;</span>
<span class="c1"># pyright: reportUnknownMemberType=false</span>
<span class="c1"># pyright: reportAttributeAccessIssue=false</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">click</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sadrive.helpers.utils</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span>
    <span class="n">get_parent_id</span><span class="p">,</span>
    <span class="n">get_dir_structure</span><span class="p">,</span>
    <span class="n">get_free_sa</span><span class="p">,</span>
    <span class="n">Generator</span><span class="p">,</span>
    <span class="n">get_file_size</span><span class="p">,</span>
    <span class="n">DirTree</span><span class="p">,</span>
    <span class="n">MAGIC_SIZE</span><span class="p">,</span>
    <span class="n">BUFFER</span><span class="p">,</span>
    <span class="n">MAX_THREADS</span><span class="p">,</span>
    <span class="n">Manifest</span><span class="p">,</span>
    <span class="n">shorten_fn</span>
<span class="p">)</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">click</span><span class="w"> </span><span class="kn">import</span> <span class="n">Context</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sadrive.helpers.drive</span><span class="w"> </span><span class="kn">import</span> <span class="n">SADrive</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">pathlib</span><span class="w"> </span><span class="kn">import</span> <span class="n">Path</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">os</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">threading</span><span class="w"> </span><span class="kn">import</span> <span class="n">Thread</span><span class="p">,</span> <span class="n">Semaphore</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">sadrive.helpers.dbf</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">dbf</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">Any</span><span class="p">,</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Union</span><span class="p">,</span> <span class="n">cast</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sadrive.commands.navigate</span><span class="w"> </span><span class="kn">import</span> <span class="n">navigate</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">json</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">rich.progress</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span>
    <span class="n">Progress</span><span class="p">,</span>
    <span class="n">SpinnerColumn</span><span class="p">,</span>
    <span class="n">BarColumn</span><span class="p">,</span>
    <span class="n">TextColumn</span><span class="p">,</span>
    <span class="n">TimeElapsedColumn</span><span class="p">,</span>
    <span class="n">TimeRemainingColumn</span><span class="p">,</span>
    <span class="n">TaskID</span><span class="p">,</span>
    <span class="n">DownloadColumn</span><span class="p">,</span>
    <span class="n">TransferSpeedColumn</span><span class="p">,</span>
<span class="p">)</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">rich.table</span><span class="w"> </span><span class="kn">import</span> <span class="n">Column</span> <span class="k">as</span> <span class="n">TableColumn</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">logging</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="n">logging</span><span class="o">.</span><span class="n">basicConfig</span><span class="p">(</span>
    <span class="n">filename</span><span class="o">=</span><span class="s2">&quot;upload.log&quot;</span><span class="p">,</span>
    <span class="n">level</span><span class="o">=</span><span class="n">logging</span><span class="o">.</span><span class="n">DEBUG</span><span class="p">,</span>
    <span class="nb">format</span><span class="o">=</span><span class="s2">&quot;</span><span class="si">%(asctime)s</span><span class="s2"> </span><span class="si">%(levelname)s</span><span class="s2"> </span><span class="si">%(message)s</span><span class="s2">&quot;</span><span class="p">,</span>
<span class="p">)</span>

<span class="n">sem</span> <span class="o">=</span> <span class="n">Semaphore</span><span class="p">(</span><span class="n">MAX_THREADS</span><span class="p">)</span>


<div class="viewcode-block" id="UploadThread">
<a class="viewcode-back" href="../../../modules/sadrive.commands.upload.html#sadrive.commands.upload.UploadThread">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">UploadThread</span><span class="p">(</span><span class="n">Thread</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Thread subclass that captures the return value of its target function.</span>

<span class="sd">    Attributes:</span>
<span class="sd">        _return: The value returned by the target function after execution.</span>

<span class="sd">    Methods:</span>
<span class="sd">        run(): Executes the target function and stores its return value.</span>
<span class="sd">        join(timeout): Joins the thread and returns the stored return value.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">group</span><span class="p">:</span> <span class="n">Any</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">target</span><span class="p">:</span> <span class="n">Any</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">name</span><span class="p">:</span> <span class="n">Any</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">args</span><span class="p">:</span> <span class="n">Any</span> <span class="o">=</span> <span class="p">(),</span>
        <span class="n">kwargs</span><span class="p">:</span> <span class="n">Any</span> <span class="o">=</span> <span class="p">{},</span>
    <span class="p">):</span>
        <span class="n">Thread</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">group</span><span class="p">,</span> <span class="n">target</span><span class="p">,</span> <span class="n">name</span><span class="p">,</span> <span class="n">args</span><span class="p">,</span> <span class="n">kwargs</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_return</span><span class="p">:</span> <span class="n">Any</span> <span class="o">=</span> <span class="kc">None</span>

<div class="viewcode-block" id="UploadThread.run">
<a class="viewcode-back" href="../../../modules/sadrive.commands.upload.html#sadrive.commands.upload.UploadThread.run">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Runs the thread, invoking the target function and storing its result.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">_target</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">_return</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">_target</span><span class="p">(</span><span class="o">*</span><span class="bp">self</span><span class="o">.</span><span class="n">_args</span><span class="p">,</span> <span class="o">**</span><span class="bp">self</span><span class="o">.</span><span class="n">_kwargs</span><span class="p">)</span></div>


<div class="viewcode-block" id="UploadThread.join">
<a class="viewcode-back" href="../../../modules/sadrive.commands.upload.html#sadrive.commands.upload.UploadThread.join">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">join</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">timeout</span><span class="p">:</span> <span class="n">Any</span> <span class="o">=</span> <span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Waits for the thread to finish and returns the function&#39;s return value.</span>

<span class="sd">        Args:</span>
<span class="sd">            timeout: Optional timeout in seconds.</span>

<span class="sd">        Returns:</span>
<span class="sd">            The return value from the target function.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">Thread</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">timeout</span><span class="p">)</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_return</span></div>
</div>



<div class="viewcode-block" id="prepare_sapart_jobs">
<a class="viewcode-back" href="../../../modules/sadrive.commands.upload.html#sadrive.commands.upload.prepare_sapart_jobs">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">prepare_sapart_jobs</span><span class="p">(</span>
    <span class="n">file_path</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">total_size</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">parent_folder_id</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">list</span><span class="p">[</span><span class="nb">tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Prepare split-upload jobs for a large file that exceeds single-account capacity.</span>

<span class="sd">    Args:</span>
<span class="sd">        file_path: Local filesystem path to the source file.</span>
<span class="sd">        total_size: Total size of the source file in bytes.</span>
<span class="sd">        parent_folder_id: Drive folder ID where the parts will be uploaded.</span>

<span class="sd">    Returns:</span>
<span class="sd">        List of tuples describing each upload job. Each tuple contains:</span>
<span class="sd">        - part_file_path (str): Path to the generated part file.</span>
<span class="sd">        - part_size (int): Size of this part in bytes.</span>
<span class="sd">        - service_account_id (str): ID of the service account chosen for this part.</span>
<span class="sd">        - remote_folder_id (str): Drive folder ID for uploading this part.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">size_map</span> <span class="o">=</span> <span class="n">dbf</span><span class="o">.</span><span class="n">get_size_map</span><span class="p">()</span>
    <span class="n">partial_sas</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">]]</span> <span class="o">=</span> <span class="p">[]</span>
    <span class="n">empty_sas</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">]]</span> <span class="o">=</span> <span class="p">[]</span>
    <span class="k">for</span> <span class="n">rec</span> <span class="ow">in</span> <span class="n">size_map</span><span class="p">:</span>
        <span class="n">sa_id</span><span class="p">,</span> <span class="n">used</span> <span class="o">=</span> <span class="n">rec</span><span class="p">[</span><span class="s2">&quot;_id&quot;</span><span class="p">],</span> <span class="n">rec</span><span class="p">[</span><span class="s2">&quot;size&quot;</span><span class="p">]</span>
        <span class="n">free</span> <span class="o">=</span> <span class="n">MAGIC_SIZE</span> <span class="o">-</span> <span class="n">used</span>
        <span class="k">if</span> <span class="n">used</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="ow">and</span> <span class="n">free</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">partial_sas</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">sa_id</span><span class="p">,</span> <span class="n">free</span><span class="p">))</span>
        <span class="k">elif</span> <span class="n">used</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">empty_sas</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">sa_id</span><span class="p">,</span> <span class="n">free</span><span class="p">))</span>

    <span class="n">candidate_sas</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">]]</span> <span class="o">=</span> <span class="p">[]</span>
    <span class="n">remaining</span> <span class="o">=</span> <span class="n">total_size</span>
    <span class="n">partial_sas</span><span class="o">.</span><span class="n">sort</span><span class="p">(</span><span class="n">key</span><span class="o">=</span><span class="k">lambda</span> <span class="n">c</span><span class="p">:</span> <span class="n">c</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>
    <span class="k">for</span> <span class="n">sa_id</span><span class="p">,</span> <span class="n">free</span> <span class="ow">in</span> <span class="n">partial_sas</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">remaining</span> <span class="o">&lt;=</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">break</span>
        <span class="n">chunk</span> <span class="o">=</span> <span class="nb">min</span><span class="p">(</span><span class="n">free</span><span class="p">,</span> <span class="n">remaining</span><span class="p">)</span>
        <span class="n">candidate_sas</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">sa_id</span><span class="p">,</span> <span class="n">chunk</span><span class="p">))</span>
        <span class="n">remaining</span> <span class="o">-=</span> <span class="n">chunk</span>

    <span class="n">it</span> <span class="o">=</span> <span class="nb">iter</span><span class="p">(</span><span class="n">empty_sas</span><span class="p">)</span>
    <span class="k">while</span> <span class="n">remaining</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="n">sa_id</span><span class="p">,</span> <span class="n">free</span> <span class="o">=</span> <span class="nb">next</span><span class="p">(</span><span class="n">it</span><span class="p">)</span>
        <span class="k">except</span> <span class="ne">StopIteration</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">RuntimeError</span><span class="p">(</span><span class="s2">&quot;Not enough SA space!&quot;</span><span class="p">)</span>
        <span class="n">chunk</span> <span class="o">=</span> <span class="nb">min</span><span class="p">(</span><span class="n">free</span><span class="p">,</span> <span class="n">remaining</span><span class="p">)</span>
        <span class="n">candidate_sas</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">sa_id</span><span class="p">,</span> <span class="n">chunk</span><span class="p">))</span>
        <span class="n">remaining</span> <span class="o">-=</span> <span class="n">chunk</span>

    <span class="n">folder_name</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">basename</span><span class="p">(</span><span class="n">file_path</span><span class="p">)</span> <span class="o">+</span> <span class="s2">&quot;.sapart&quot;</span>
    <span class="n">drive</span> <span class="o">=</span> <span class="n">SADrive</span><span class="p">(</span><span class="s2">&quot;0&quot;</span><span class="p">)</span>
    <span class="n">sapart_folder_id</span> <span class="o">=</span> <span class="n">drive</span><span class="o">.</span><span class="n">create_folder</span><span class="p">(</span><span class="n">folder_name</span><span class="p">,</span> <span class="n">parent_folder_id</span><span class="p">)</span>

    <span class="n">jobs</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]</span> <span class="o">=</span> <span class="p">[]</span>
    <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">file_path</span><span class="p">,</span> <span class="s2">&quot;rb&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">src</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">idx</span><span class="p">,</span> <span class="p">(</span><span class="n">sa_id</span><span class="p">,</span> <span class="n">chunk_size</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">candidate_sas</span><span class="p">,</span> <span class="n">start</span><span class="o">=</span><span class="mi">1</span><span class="p">):</span>
            <span class="n">part_name</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">basename</span><span class="p">(</span><span class="n">file_path</span><span class="p">)</span><span class="si">}</span><span class="s2">.sapart</span><span class="si">{</span><span class="n">idx</span><span class="si">}</span><span class="s2">&quot;</span>
            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">part_name</span><span class="p">,</span> <span class="s2">&quot;wb&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">dst</span><span class="p">:</span>
                <span class="n">to_go</span> <span class="o">=</span> <span class="n">chunk_size</span>
                <span class="k">while</span> <span class="n">to_go</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                    <span class="n">buf</span> <span class="o">=</span> <span class="n">src</span><span class="o">.</span><span class="n">read</span><span class="p">(</span><span class="nb">min</span><span class="p">(</span><span class="n">BUFFER</span><span class="p">,</span> <span class="n">to_go</span><span class="p">))</span>
                    <span class="n">dst</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">buf</span><span class="p">)</span>
                    <span class="n">to_go</span> <span class="o">-=</span> <span class="nb">len</span><span class="p">(</span><span class="n">buf</span><span class="p">)</span>
            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Prepared </span><span class="si">{</span><span class="n">part_name</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">chunk_size</span><span class="si">}</span><span class="s2"> bytes → SA </span><span class="si">{</span><span class="n">sa_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="n">jobs</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">part_name</span><span class="p">,</span> <span class="n">chunk_size</span><span class="p">,</span> <span class="n">sapart_folder_id</span><span class="p">,</span> <span class="nb">str</span><span class="p">(</span><span class="n">sa_id</span><span class="p">)))</span>

    <span class="n">manifest</span><span class="p">:</span> <span class="n">Manifest</span> <span class="o">=</span> <span class="p">{</span>
        <span class="s2">&quot;original_filename&quot;</span><span class="p">:</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">basename</span><span class="p">(</span><span class="n">file_path</span><span class="p">),</span>
        <span class="s2">&quot;parts&quot;</span><span class="p">:</span> <span class="p">[</span>
            <span class="p">{</span><span class="s2">&quot;filename&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">basename</span><span class="p">(</span><span class="n">file_path</span><span class="p">)</span><span class="si">}</span><span class="s2">.sapart</span><span class="si">{</span><span class="n">idx</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="s2">&quot;size&quot;</span><span class="p">:</span> <span class="n">size</span><span class="p">}</span>
            <span class="k">for</span> <span class="n">idx</span><span class="p">,</span> <span class="p">(</span><span class="n">_</span><span class="p">,</span> <span class="n">size</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">candidate_sas</span><span class="p">,</span> <span class="n">start</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
        <span class="p">],</span>
    <span class="p">}</span>
    <span class="n">mname</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">basename</span><span class="p">(</span><span class="n">file_path</span><span class="p">)</span> <span class="o">+</span> <span class="s2">&quot;.sapart.manifest.json&quot;</span>
    <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">mname</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">mf</span><span class="p">:</span>
        <span class="n">json</span><span class="o">.</span><span class="n">dump</span><span class="p">(</span><span class="n">manifest</span><span class="p">,</span> <span class="n">mf</span><span class="p">,</span> <span class="n">indent</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>
    <span class="n">jobs</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">mname</span><span class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">getsize</span><span class="p">(</span><span class="n">mname</span><span class="p">),</span> <span class="n">sapart_folder_id</span><span class="p">,</span> <span class="s2">&quot;-1&quot;</span><span class="p">))</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Prepared manifest: </span><span class="si">{</span><span class="n">mname</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">jobs</span></div>



<div class="viewcode-block" id="upload_file">
<a class="viewcode-back" href="../../../modules/sadrive.commands.upload.html#sadrive.commands.upload.upload_file">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">upload_file</span><span class="p">(</span>
    <span class="n">file_path</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
    <span class="n">size</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">parent_folder_id</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
    <span class="n">progress</span><span class="p">:</span> <span class="n">Progress</span><span class="p">,</span>
    <span class="n">task_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">sa_num_provided</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span><span class="p">,</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Union</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">]]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Upload a file or part to SA-Drive, streaming with progress tracking.</span>

<span class="sd">    Args:</span>
<span class="sd">        file_path: Local path to the file or part to upload.</span>
<span class="sd">        size: Total size of the upload in bytes.</span>
<span class="sd">        parent_folder_id: Drive folder ID where the file will be stored.</span>
<span class="sd">        progress: Rich Progress instance for updating the progress bar.</span>
<span class="sd">        task_id: Task identifier for Rich progress updates.</span>
<span class="sd">        sa_num_provided: Optional service account ID to use; if empty, choose automatically.</span>

<span class="sd">    Returns:</span>
<span class="sd">        Dict containing details of the uploaded file from the Drive API response,</span>
<span class="sd">        including &#39;id&#39;, &#39;title&#39;, &#39;parents&#39;, and &#39;fileSize&#39;.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">start</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
    <span class="n">last_bytes</span> <span class="o">=</span> <span class="mi">0</span>
    <span class="k">if</span> <span class="n">sa_num_provided</span> <span class="o">==</span> <span class="s2">&quot;&quot;</span><span class="p">:</span>
        <span class="n">sa_numbers</span> <span class="o">=</span> <span class="n">get_free_sa</span><span class="p">(</span><span class="n">dbf</span><span class="o">.</span><span class="n">get_size_map</span><span class="p">(),</span> <span class="n">size</span><span class="p">)</span>
        <span class="n">sa_number</span> <span class="o">=</span> <span class="nb">str</span><span class="p">(</span><span class="n">sa_numbers</span><span class="p">[</span><span class="mi">0</span><span class="p">])</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">sa_number</span> <span class="o">=</span> <span class="n">sa_num_provided</span>
    <span class="n">drive</span> <span class="o">=</span> <span class="n">SADrive</span><span class="p">(</span><span class="n">sa_number</span><span class="p">)</span>
    <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">file_path</span><span class="p">,</span> <span class="s2">&quot;rb&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">stream_bytes</span><span class="p">:</span>
        <span class="n">filename</span> <span class="o">=</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">basename</span><span class="p">(</span><span class="n">file_path</span><span class="p">)</span>
        <span class="n">worker</span> <span class="o">=</span> <span class="n">Generator</span><span class="p">(</span><span class="n">drive</span><span class="o">.</span><span class="n">upload_file</span><span class="p">(</span><span class="n">filename</span><span class="p">,</span> <span class="n">parent_folder_id</span><span class="p">,</span> <span class="n">stream_bytes</span><span class="p">))</span>
        <span class="k">for</span> <span class="n">prog</span> <span class="ow">in</span> <span class="n">worker</span><span class="p">:</span>
            <span class="n">bytes_done</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">prog</span> <span class="o">/</span> <span class="mi">100</span> <span class="o">*</span> <span class="n">size</span><span class="p">)</span>
            <span class="n">delta</span> <span class="o">=</span> <span class="n">bytes_done</span> <span class="o">-</span> <span class="n">last_bytes</span>
            <span class="n">last_bytes</span> <span class="o">=</span> <span class="n">bytes_done</span>
            
            <span class="n">elapsed</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">start</span>
            <span class="n">speed</span> <span class="o">=</span> <span class="n">bytes_done</span> <span class="o">/</span> <span class="n">elapsed</span> <span class="k">if</span> <span class="n">elapsed</span> <span class="o">&gt;</span> <span class="mi">0</span> <span class="k">else</span> <span class="mi">0</span>
            <span class="n">progress</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">cast</span><span class="p">(</span><span class="n">TaskID</span><span class="p">,</span> <span class="n">task_id</span><span class="p">),</span> <span class="n">advance</span><span class="o">=</span><span class="n">delta</span><span class="p">)</span>
            <span class="n">logging</span><span class="o">.</span><span class="n">debug</span><span class="p">(</span>
                <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">file_path</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">prog</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">% → &quot;</span> <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">bytes_done</span><span class="si">}</span><span class="s2">/</span><span class="si">{</span><span class="n">size</span><span class="si">}</span><span class="s2"> B, </span><span class="si">{</span><span class="n">speed</span><span class="si">:</span><span class="s2">.2f</span><span class="si">}</span><span class="s2"> B/s&quot;</span>
            <span class="p">)</span>
        
        <span class="k">if</span> <span class="n">last_bytes</span> <span class="o">&lt;</span> <span class="n">size</span><span class="p">:</span>
            <span class="n">progress</span><span class="o">.</span><span class="n">update</span><span class="p">(</span><span class="n">cast</span><span class="p">(</span><span class="n">TaskID</span><span class="p">,</span> <span class="n">task_id</span><span class="p">),</span> <span class="n">advance</span><span class="o">=</span><span class="p">(</span><span class="n">size</span> <span class="o">-</span> <span class="n">last_bytes</span><span class="p">))</span>

    <span class="n">uled_file_details</span> <span class="o">=</span> <span class="n">worker</span><span class="o">.</span><span class="n">value</span>

    <span class="n">dbf</span><span class="o">.</span><span class="n">insert_file</span><span class="p">(</span>
        <span class="n">uled_file_details</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">],</span>
        <span class="n">uled_file_details</span><span class="p">[</span><span class="s2">&quot;title&quot;</span><span class="p">],</span>
        <span class="n">uled_file_details</span><span class="p">[</span><span class="s2">&quot;parents&quot;</span><span class="p">][</span><span class="mi">0</span><span class="p">][</span><span class="s2">&quot;id&quot;</span><span class="p">],</span>
        <span class="nb">int</span><span class="p">(</span><span class="n">uled_file_details</span><span class="p">[</span><span class="s2">&quot;fileSize&quot;</span><span class="p">]),</span>
        <span class="s2">&quot;file&quot;</span><span class="p">,</span>
        <span class="nb">str</span><span class="p">(</span><span class="n">sa_number</span><span class="p">),</span>
        <span class="kc">False</span><span class="p">,</span>
    <span class="p">)</span>
    <span class="k">return</span> <span class="n">uled_file_details</span></div>



<div class="viewcode-block" id="sem_upload_wrapper">
<a class="viewcode-back" href="../../../modules/sadrive.commands.upload.html#sadrive.commands.upload.sem_upload_wrapper">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">sem_upload_wrapper</span><span class="p">(</span>
    <span class="n">fp</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">sz</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">pid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">progress</span><span class="p">:</span> <span class="n">Progress</span><span class="p">,</span> <span class="n">task_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">sa</span><span class="p">:</span> <span class="nb">str</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Wrapper for uploading a file that ensures semaphore release after completion.</span>

<span class="sd">    Args:</span>
<span class="sd">        fp: Local file path to upload.</span>
<span class="sd">        sz: File size in bytes.</span>
<span class="sd">        pid: Drive parent folder ID.</span>
<span class="sd">        progress: Rich Progress instance for tracking upload progress.</span>
<span class="sd">        task_id: Identifier for the progress task.</span>
<span class="sd">        sa: Service account ID to use for this upload.</span>

<span class="sd">    Behavior:</span>
<span class="sd">        - Calls `upload_file` with provided arguments.</span>
<span class="sd">        - Ensures the global `sem` is released even if an error occurs.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">upload_file</span><span class="p">(</span><span class="n">fp</span><span class="p">,</span> <span class="n">sz</span><span class="p">,</span> <span class="n">pid</span><span class="p">,</span> <span class="n">progress</span><span class="p">,</span> <span class="n">task_id</span><span class="p">,</span> <span class="n">sa</span><span class="p">)</span>
    <span class="k">finally</span><span class="p">:</span>
        <span class="n">sem</span><span class="o">.</span><span class="n">release</span><span class="p">()</span></div>



<div class="viewcode-block" id="reflect_structure_on_sadrive">
<a class="viewcode-back" href="../../../modules/sadrive.commands.upload.html#sadrive.commands.upload.reflect_structure_on_sadrive">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">reflect_structure_on_sadrive</span><span class="p">(</span>
    <span class="n">structure</span><span class="p">:</span> <span class="n">DirTree</span><span class="p">,</span>
    <span class="n">destination</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
    <span class="n">parent_id_map</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">]],</span>
    <span class="n">tmp_drive</span><span class="p">:</span> <span class="n">SADrive</span><span class="p">,</span>
    <span class="n">path</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">str</span><span class="p">],</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Recursively reflect a local directory tree structure on SA-Drive by creating folders and scheduling file uploads.</span>

<span class="sd">    Args:</span>
<span class="sd">        structure: Nested dictionary mapping folder names to subtrees or file sizes.</span>
<span class="sd">        destination: Drive folder ID where this level&#39;s content should be mirrored.</span>
<span class="sd">        parent_id_map: List tracking tuples of (folder_name, local_size, drive_folder_id) for created folders.</span>
<span class="sd">        tmp_drive: Authenticated SADrive instance used for folder creation.</span>
<span class="sd">        path: Accumulated list of path segments representing the current traversal.</span>

<span class="sd">    Behavior:</span>
<span class="sd">        - Iterates through the `structure` dict:</span>
<span class="sd">          - For sub-dictionaries, creates a corresponding folder on Drive, updates `parent_id_map`, and recurses.</span>
<span class="sd">          - For file entries (size values), schedules upload jobs for each file part.</span>
<span class="sd">        - Does not perform actual uploads; integrates with the main `upload` command logic.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="n">structure</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="nb">int</span><span class="p">):</span>
            <span class="n">path</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>
            <span class="n">parent_id_map</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="s2">&quot;</span><span class="se">\\</span><span class="s2">&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">path</span><span class="p">),</span> <span class="n">value</span><span class="p">,</span> <span class="n">destination</span><span class="p">))</span>
            <span class="n">path</span><span class="o">.</span><span class="n">pop</span><span class="p">()</span>
        <span class="k">else</span><span class="p">:</span>  <span class="c1"># Value is dirtree</span>
            <span class="n">nf_id</span> <span class="o">=</span> <span class="n">tmp_drive</span><span class="o">.</span><span class="n">create_folder</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">destination</span><span class="p">)</span>
            <span class="n">dbf</span><span class="o">.</span><span class="n">insert_file</span><span class="p">(</span><span class="n">nf_id</span><span class="p">,</span> <span class="n">key</span><span class="p">,</span> <span class="n">destination</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;folder&quot;</span><span class="p">,</span> <span class="s2">&quot;0&quot;</span><span class="p">,</span> <span class="kc">False</span><span class="p">)</span>
            <span class="n">path</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>
            <span class="n">reflect_structure_on_sadrive</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">nf_id</span><span class="p">,</span> <span class="n">parent_id_map</span><span class="p">,</span> <span class="n">tmp_drive</span><span class="p">,</span> <span class="n">path</span><span class="p">)</span>
            <span class="n">path</span><span class="o">.</span><span class="n">pop</span><span class="p">()</span>
    <span class="k">return</span></div>



<span class="nd">@click</span><span class="o">.</span><span class="n">command</span><span class="p">()</span>
<span class="nd">@click</span><span class="o">.</span><span class="n">argument</span><span class="p">(</span><span class="s2">&quot;path&quot;</span><span class="p">)</span>
<span class="nd">@click</span><span class="o">.</span><span class="n">argument</span><span class="p">(</span><span class="s2">&quot;destination&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="n">get_parent_id</span><span class="p">())</span>
<span class="nd">@click</span><span class="o">.</span><span class="n">pass_context</span>
<span class="k">def</span><span class="w"> </span><span class="nf">upload</span><span class="p">(</span><span class="n">ctx</span><span class="p">:</span> <span class="n">Context</span><span class="p">,</span> <span class="n">path</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">destination</span><span class="p">:</span> <span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Upload a folder or file to SA-Drive at a specified destination folder ID.</span>

<span class="sd">    Args:</span>
<span class="sd">        ctx: Click context for invoking subcommands.</span>
<span class="sd">        path: Local filesystem path to the file or directory to upload.</span>
<span class="sd">        destination: Drive folder ID where the content will be uploaded (default: root).</span>

<span class="sd">    Behavior:</span>
<span class="sd">        - Determines if `path` is a file or directory.</span>
<span class="sd">        - For files larger than single-account capacity, splits into parts and uploads.</span>
<span class="sd">        - For directories, reflects structure on SA-Drive and uploads all contents.</span>
<span class="sd">        - Updates local database mappings and service account usage.</span>
<span class="sd">        - Provides real-time progress via Rich.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">fd</span> <span class="o">=</span> <span class="n">dbf</span><span class="o">.</span><span class="n">get_file_details</span><span class="p">(</span><span class="n">destination</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">destination</span> <span class="o">!=</span> <span class="n">get_parent_id</span><span class="p">():</span>
        <span class="k">if</span> <span class="p">(</span><span class="ow">not</span> <span class="n">fd</span><span class="p">)</span> <span class="ow">or</span> <span class="n">fd</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;file&quot;</span><span class="p">:</span>
            <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="s2">&quot;Destination folder id does not exist !!&quot;</span><span class="p">)</span>
            <span class="k">return</span>
    <span class="n">pathp</span><span class="p">:</span> <span class="n">Path</span> <span class="o">=</span> <span class="n">Path</span><span class="p">(</span><span class="n">path</span><span class="p">)</span><span class="o">.</span><span class="n">absolute</span><span class="p">()</span>

    <span class="n">progress</span> <span class="o">=</span> <span class="n">Progress</span><span class="p">(</span>
        <span class="n">SpinnerColumn</span><span class="p">(</span><span class="n">table_column</span><span class="o">=</span><span class="n">TableColumn</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">)),</span>
        <span class="n">TextColumn</span><span class="p">(</span><span class="s2">&quot;[bold blue]</span><span class="si">{task.fields[filename]}</span><span class="s2">&quot;</span><span class="p">,</span> 
               <span class="n">table_column</span><span class="o">=</span><span class="n">TableColumn</span><span class="p">(</span><span class="s2">&quot;File&quot;</span><span class="p">),</span> <span class="n">justify</span><span class="o">=</span><span class="s2">&quot;right&quot;</span><span class="p">),</span>
        <span class="n">BarColumn</span><span class="p">(</span><span class="n">table_column</span><span class="o">=</span><span class="n">TableColumn</span><span class="p">(</span><span class="s2">&quot;Progress&quot;</span><span class="p">)),</span>
        <span class="n">DownloadColumn</span><span class="p">(</span><span class="n">binary_units</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span><span class="n">table_column</span><span class="o">=</span><span class="n">TableColumn</span><span class="p">(</span><span class="s2">&quot;Size&quot;</span><span class="p">)),</span>
        <span class="n">TransferSpeedColumn</span><span class="p">(</span><span class="n">table_column</span><span class="o">=</span><span class="n">TableColumn</span><span class="p">(</span><span class="s2">&quot;Speed&quot;</span><span class="p">)),</span> 
        <span class="n">TimeElapsedColumn</span><span class="p">(</span><span class="n">table_column</span><span class="o">=</span><span class="n">TableColumn</span><span class="p">(</span><span class="s2">&quot;Elapsed&quot;</span><span class="p">)),</span>
        <span class="n">TimeRemainingColumn</span><span class="p">(</span><span class="n">table_column</span><span class="o">=</span><span class="n">TableColumn</span><span class="p">(</span><span class="s2">&quot;ETA&quot;</span><span class="p">)),</span>  
    <span class="p">)</span>
    <span class="n">progress</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>

    <span class="k">if</span> <span class="n">Path</span><span class="o">.</span><span class="n">is_dir</span><span class="p">(</span><span class="n">pathp</span><span class="p">):</span>
        <span class="n">structure</span> <span class="o">=</span> <span class="n">get_dir_structure</span><span class="p">(</span><span class="n">pathp</span><span class="p">)</span>
        <span class="c1"># (filepath,size,parent_id)</span>
        <span class="n">parent_id_map</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="n">spath</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;..&quot;</span><span class="p">]</span>
        <span class="n">tmp_drive</span><span class="p">:</span> <span class="n">SADrive</span> <span class="o">=</span> <span class="n">SADrive</span><span class="p">(</span><span class="s2">&quot;0&quot;</span><span class="p">)</span>
        <span class="n">reflect_structure_on_sadrive</span><span class="p">(</span>
            <span class="n">structure</span><span class="p">,</span> <span class="n">destination</span><span class="p">,</span> <span class="n">parent_id_map</span><span class="p">,</span> <span class="n">tmp_drive</span><span class="p">,</span> <span class="n">spath</span>
        <span class="p">)</span>
        <span class="n">parent_id_map</span><span class="o">.</span><span class="n">sort</span><span class="p">(</span><span class="n">key</span><span class="o">=</span><span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="n">x</span><span class="p">[</span><span class="mi">1</span><span class="p">])</span>
        <span class="n">sa_map_copy</span> <span class="o">=</span> <span class="p">[[</span><span class="n">i</span><span class="p">[</span><span class="s2">&quot;_id&quot;</span><span class="p">],</span> <span class="n">i</span><span class="p">[</span><span class="s2">&quot;size&quot;</span><span class="p">]]</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">dbf</span><span class="o">.</span><span class="n">get_size_map</span><span class="p">()]</span>
        <span class="n">file_sa_num_map</span><span class="p">:</span> <span class="nb">dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="c1"># filepath - sanumber</span>
        <span class="n">upload_threads</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="n">UploadThread</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="n">large_files</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">tuple</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">]]</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="k">for</span> <span class="n">file_path</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="n">dest_id</span> <span class="ow">in</span> <span class="n">parent_id_map</span><span class="p">:</span>
            <span class="n">candidates</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">]]</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="k">for</span> <span class="n">idx</span><span class="p">,</span> <span class="p">(</span><span class="n">sa_id</span><span class="p">,</span> <span class="n">used</span><span class="p">)</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">sa_map_copy</span><span class="p">):</span>
                <span class="n">free</span> <span class="o">=</span> <span class="n">MAGIC_SIZE</span> <span class="o">-</span> <span class="n">used</span>
                <span class="k">if</span> <span class="n">free</span> <span class="o">&gt;=</span> <span class="n">size</span><span class="p">:</span>
                    <span class="n">candidates</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">idx</span><span class="p">,</span> <span class="n">sa_id</span><span class="p">,</span> <span class="n">used</span><span class="p">,</span> <span class="n">free</span> <span class="o">-</span> <span class="n">size</span><span class="p">))</span>

            <span class="k">if</span> <span class="ow">not</span> <span class="n">candidates</span><span class="p">:</span>
                <span class="n">large_files</span><span class="o">.</span><span class="n">append</span><span class="p">((</span><span class="n">file_path</span><span class="p">,</span> <span class="n">size</span><span class="p">,</span> <span class="n">dest_id</span><span class="p">))</span>
                <span class="k">continue</span>
                <span class="c1"># raise RuntimeError(f&quot;Cannot assign file {file_path}, size {size}: no SA has enough space.&quot;)</span>

            <span class="n">best_idx</span><span class="p">,</span> <span class="n">best_sa_id</span><span class="p">,</span> <span class="n">_</span><span class="p">,</span> <span class="n">_</span> <span class="o">=</span> <span class="nb">min</span><span class="p">(</span><span class="n">candidates</span><span class="p">,</span> <span class="n">key</span><span class="o">=</span><span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="n">x</span><span class="p">[</span><span class="mi">3</span><span class="p">])</span>

            <span class="n">sa_map_copy</span><span class="p">[</span><span class="n">best_idx</span><span class="p">][</span><span class="mi">1</span><span class="p">]</span> <span class="o">+=</span> <span class="n">size</span>
            <span class="n">file_sa_num_map</span><span class="p">[</span><span class="n">file_path</span><span class="p">]</span> <span class="o">=</span> <span class="n">best_sa_id</span>

            <span class="n">task_id</span> <span class="o">=</span> <span class="n">progress</span><span class="o">.</span><span class="n">add_task</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="n">total</span><span class="o">=</span><span class="n">size</span><span class="p">,</span> <span class="n">filename</span><span class="o">=</span><span class="n">shorten_fn</span><span class="p">(</span><span class="n">file_path</span><span class="p">))</span>
            <span class="n">sem</span><span class="o">.</span><span class="n">acquire</span><span class="p">()</span>
            <span class="n">t</span> <span class="o">=</span> <span class="n">UploadThread</span><span class="p">(</span>
                <span class="n">target</span><span class="o">=</span><span class="n">sem_upload_wrapper</span><span class="p">,</span>
                <span class="n">args</span><span class="o">=</span><span class="p">(</span>
                    <span class="n">pathp</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="n">file_path</span><span class="p">)</span><span class="o">.</span><span class="n">resolve</span><span class="p">(),</span>
                    <span class="n">size</span><span class="p">,</span>
                    <span class="n">dest_id</span><span class="p">,</span>
                    <span class="n">progress</span><span class="p">,</span>
                    <span class="n">task_id</span><span class="p">,</span>
                    <span class="n">best_sa_id</span><span class="p">,</span>
                <span class="p">),</span>
            <span class="p">)</span>
            <span class="n">upload_threads</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">t</span><span class="p">)</span>
            <span class="n">t</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>

        <span class="k">for</span> <span class="n">fp</span><span class="p">,</span> <span class="n">sz</span><span class="p">,</span> <span class="n">did</span> <span class="ow">in</span> <span class="n">large_files</span><span class="p">:</span>
            <span class="n">jobs</span> <span class="o">=</span> <span class="n">prepare_sapart_jobs</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">pathp</span><span class="o">.</span><span class="n">joinpath</span><span class="p">(</span><span class="n">fp</span><span class="p">)</span><span class="o">.</span><span class="n">resolve</span><span class="p">()),</span> <span class="n">sz</span><span class="p">,</span> <span class="n">did</span><span class="p">)</span>
            <span class="k">for</span> <span class="n">job</span> <span class="ow">in</span> <span class="n">jobs</span><span class="p">:</span>
                <span class="c1"># job == (part_path, part_size, sapart_folder_id, sa_id)</span>
                <span class="n">task_id</span> <span class="o">=</span> <span class="n">progress</span><span class="o">.</span><span class="n">add_task</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="n">total</span><span class="o">=</span><span class="n">sz</span><span class="p">,</span> <span class="n">filename</span><span class="o">=</span><span class="n">shorten_fn</span><span class="p">(</span><span class="n">fp</span><span class="p">))</span>
                <span class="n">sem</span><span class="o">.</span><span class="n">acquire</span><span class="p">()</span>
                <span class="n">jj</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="n">Any</span><span class="p">]</span> <span class="o">=</span> <span class="n">cast</span><span class="p">(</span><span class="n">Any</span><span class="p">,</span> <span class="nb">list</span><span class="p">(</span><span class="n">job</span><span class="p">))</span>
                <span class="k">if</span> <span class="n">job</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;-1&quot;</span><span class="p">:</span>
                    <span class="n">jj</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="o">=</span> <span class="n">progress</span>
                    <span class="n">jj</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">task_id</span><span class="p">)</span>
                    <span class="n">jj</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">)</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">sasa</span> <span class="o">=</span> <span class="n">jj</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
                    <span class="n">jj</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="o">=</span> <span class="n">progress</span>
                    <span class="n">jj</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">task_id</span><span class="p">)</span>
                    <span class="n">jj</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">sasa</span><span class="p">)</span>

                <span class="n">t</span> <span class="o">=</span> <span class="n">UploadThread</span><span class="p">(</span><span class="n">target</span><span class="o">=</span><span class="n">sem_upload_wrapper</span><span class="p">,</span> <span class="n">args</span><span class="o">=</span><span class="nb">tuple</span><span class="p">(</span><span class="n">jj</span><span class="p">))</span>
                <span class="n">upload_threads</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">t</span><span class="p">)</span>
                <span class="n">t</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>

        <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">upload_threads</span><span class="p">:</span>
            <span class="n">x</span><span class="o">.</span><span class="n">join</span><span class="p">()</span>

    <span class="k">else</span><span class="p">:</span>

        <span class="n">sz</span> <span class="o">=</span> <span class="n">get_file_size</span><span class="p">(</span><span class="n">pathp</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">sz</span> <span class="o">&gt;</span> <span class="n">MAGIC_SIZE</span><span class="p">:</span>
            <span class="n">upload_threads</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="n">UploadThread</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="n">jobs</span> <span class="o">=</span> <span class="n">prepare_sapart_jobs</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">pathp</span><span class="p">),</span> <span class="n">sz</span><span class="p">,</span> <span class="n">destination</span><span class="p">)</span>

            <span class="k">for</span> <span class="n">job</span> <span class="ow">in</span> <span class="n">jobs</span><span class="p">:</span>
                <span class="c1"># job == (part_path, part_size, sapart_folder_id, sa_id)</span>
                <span class="n">task_id</span> <span class="o">=</span> <span class="n">progress</span><span class="o">.</span><span class="n">add_task</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="n">total</span><span class="o">=</span><span class="n">job</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="n">filename</span><span class="o">=</span><span class="n">shorten_fn</span><span class="p">(</span><span class="n">job</span><span class="p">[</span><span class="mi">0</span><span class="p">]))</span>
                <span class="n">sem</span><span class="o">.</span><span class="n">acquire</span><span class="p">()</span>
                <span class="n">jj</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span class="n">Any</span><span class="p">]</span> <span class="o">=</span> <span class="n">cast</span><span class="p">(</span><span class="n">Any</span><span class="p">,</span> <span class="nb">list</span><span class="p">(</span><span class="n">job</span><span class="p">))</span>
                <span class="k">if</span> <span class="n">job</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;-1&quot;</span><span class="p">:</span>
                    <span class="n">jj</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="o">=</span> <span class="n">progress</span>
                    <span class="n">jj</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">task_id</span><span class="p">)</span>
                    <span class="n">jj</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">)</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">sasa</span> <span class="o">=</span> <span class="n">jj</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span>
                    <span class="n">jj</span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="o">=</span> <span class="n">progress</span>
                    <span class="n">jj</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">task_id</span><span class="p">)</span>
                    <span class="n">jj</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">sasa</span><span class="p">)</span>
                <span class="n">t</span> <span class="o">=</span> <span class="n">UploadThread</span><span class="p">(</span><span class="n">target</span><span class="o">=</span><span class="n">sem_upload_wrapper</span><span class="p">,</span> <span class="n">args</span><span class="o">=</span><span class="nb">tuple</span><span class="p">(</span><span class="n">jj</span><span class="p">))</span>
                <span class="n">upload_threads</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">t</span><span class="p">)</span>
                <span class="n">t</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>

            <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="n">upload_threads</span><span class="p">:</span>
                <span class="n">x</span><span class="o">.</span><span class="n">join</span><span class="p">()</span>

        <span class="k">else</span><span class="p">:</span>
            <span class="n">task_id</span> <span class="o">=</span> <span class="n">progress</span><span class="o">.</span><span class="n">add_task</span><span class="p">(</span><span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="n">total</span><span class="o">=</span><span class="n">sz</span><span class="p">,</span> <span class="n">filename</span><span class="o">=</span><span class="n">shorten_fn</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">pathp</span><span class="p">)))</span>
            <span class="n">t</span> <span class="o">=</span> <span class="n">UploadThread</span><span class="p">(</span>
                <span class="n">target</span><span class="o">=</span><span class="n">upload_file</span><span class="p">,</span>
                <span class="n">args</span><span class="o">=</span><span class="p">(</span><span class="n">pathp</span><span class="p">,</span> <span class="n">sz</span><span class="p">,</span> <span class="n">destination</span><span class="p">,</span> <span class="n">progress</span><span class="p">,</span> <span class="n">task_id</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">),</span>
            <span class="p">)</span>
            <span class="n">t</span><span class="o">.</span><span class="n">start</span><span class="p">()</span>
            <span class="n">t</span><span class="o">.</span><span class="n">join</span><span class="p">()</span>
    <span class="n">progress</span><span class="o">.</span><span class="n">stop</span><span class="p">()</span>
    <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="s2">&quot;Uploaded the file/folder! Navigate it:&quot;</span><span class="p">)</span>
    <span class="n">ctx</span><span class="o">.</span><span class="n">invoke</span><span class="p">(</span><span class="n">navigate</span><span class="p">,</span> <span class="n">folderid</span><span class="o">=</span><span class="n">destination</span><span class="p">)</span>
</pre></div>

                </article>
              
              
              
              
              
                <footer class="prev-next-footer d-print-none">
                  
<div class="prev-next-area">
</div>
                </footer>
              
            </div>
            
            
              
            
          </div>
          <footer class="bd-footer-content">
            
          </footer>
        
      </main>
    </div>
  </div>
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script defer src="../../../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf"></script>
<script defer src="../../../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf"></script>

  <footer class="bd-footer">
<div class="bd-footer__inner bd-page-width">
  
    <div class="footer-items__start">
      
        <div class="footer-item">

  <p class="copyright">
    
      © Copyright 2025, jsmaskeen.
      <br/>
    
  </p>
</div>
      
        <div class="footer-item">

  <p class="sphinx-version">
    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    <br/>
  </p>
</div>
      
    </div>
  
  
  
    <div class="footer-items__end">
      
        <div class="footer-item">
<p class="theme-version">
  <!-- # L10n: Setting the PST URL as an argument as this does not need to be localized -->
  Built with the <a href="https://pydata-sphinx-theme.readthedocs.io/en/stable/index.html">PyData Sphinx Theme</a> 0.16.1.
</p></div>
      
    </div>
  
</div>

  </footer>
  </body>
</html>