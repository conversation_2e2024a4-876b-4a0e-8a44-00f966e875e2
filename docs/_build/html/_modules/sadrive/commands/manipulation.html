
<!DOCTYPE html>


<html lang="en" data-content_root="../../../" >

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>sadrive.commands.manipulation &#8212; sadrive 2.0.0 documentation</title>
  
  
  
  <script data-cfasync="false">
    document.documentElement.dataset.mode = localStorage.getItem("mode") || "";
    document.documentElement.dataset.theme = localStorage.getItem("theme") || "";
  </script>
  <!--
    this give us a css class that will be invisible only if js is disabled
  -->
  <noscript>
    <style>
      .pst-js-only { display: none !important; }

    </style>
  </noscript>
  
  <!-- Loaded before other Sphinx assets -->
  <link href="../../../_static/styles/theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />
<link href="../../../_static/styles/pydata-sphinx-theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />

    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=8f2a1f02" />
  
  <!-- So that users can add custom icons -->
  <script src="../../../_static/scripts/fontawesome.js?digest=8878045cc6db502f8baf"></script>
  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="../../../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf" />
<link rel="preload" as="script" href="../../../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf" />

    <script src="../../../_static/documentation_options.js?v=51b770b3"></script>
    <script src="../../../_static/doctools.js?v=9bcbadda"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script>DOCUMENTATION_OPTIONS.pagename = '_modules/sadrive/commands/manipulation';</script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
  <meta name="viewport" content="width=device-width, initial-scale=1"/>
  <meta name="docsearch:language" content="en"/>
  <meta name="docsearch:version" content="" />
  </head>
  
  
  <body data-bs-spy="scroll" data-bs-target=".bd-toc-nav" data-offset="180" data-bs-root-margin="0px 0px -60%" data-default-mode="">

  
  
  <div id="pst-skip-link" class="skip-link d-print-none"><a href="#main-content">Skip to main content</a></div>
  
  <div id="pst-scroll-pixel-helper"></div>
  
  <button type="button" class="btn rounded-pill" id="pst-back-to-top">
    <i class="fa-solid fa-arrow-up"></i>Back to top</button>

  
  <dialog id="pst-search-dialog">
    
<form class="bd-search d-flex align-items-center"
      action="../../../search.html"
      method="get">
  <i class="fa-solid fa-magnifying-glass"></i>
  <input type="search"
         class="form-control"
         name="q"
         placeholder="Search the docs ..."
         aria-label="Search the docs ..."
         autocomplete="off"
         autocorrect="off"
         autocapitalize="off"
         spellcheck="false"/>
  <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd>K</kbd></span>
</form>
  </dialog>

  <div class="pst-async-banner-revealer d-none">
  <aside id="bd-header-version-warning" class="d-none d-print-none" aria-label="Version warning"></aside>
</div>

  
    <header class="bd-header navbar navbar-expand-lg bd-navbar d-print-none">
<div class="bd-header__inner bd-page-width">
  <button class="pst-navbar-icon sidebar-toggle primary-toggle" aria-label="Site navigation">
    <span class="fa-solid fa-bars"></span>
  </button>
  
  
  <div class="col-lg-3 navbar-header-items__start">
    
      <div class="navbar-item">

  
    
  

<a class="navbar-brand logo" href="../../../index.html">
  
  
  
  
  
  
    <p class="title logo__title">sadrive 2.0.0 documentation</p>
  
</a></div>
    
  </div>
  
  <div class="col-lg-9 navbar-header-items">
    
    <div class="me-auto navbar-header-items__center">
      
        <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
      
    </div>
    
    
    <div class="navbar-header-items__end">
      
        <div class="navbar-item navbar-persistent--container">
          

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
        </div>
      
      
        <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
      
    </div>
    
  </div>
  
  
    <div class="navbar-persistent--mobile">

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
    </div>
  

  
</div>

    </header>
  

  <div class="bd-container">
    <div class="bd-container__inner bd-page-width">
      
      
      
        
      
      <dialog id="pst-primary-sidebar-modal"></dialog>
      <div id="pst-primary-sidebar" class="bd-sidebar-primary bd-sidebar hide-on-wide">
        

  
  <div class="sidebar-header-items sidebar-primary__section">
    
    
      <div class="sidebar-header-items__center">
        
          
          
            <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
          
        
      </div>
    
    
    
      <div class="sidebar-header-items__end">
        
          <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
        
      </div>
    
  </div>
  
  
  <div class="sidebar-primary-items__end sidebar-primary__section">
      <div class="sidebar-primary-item">
<div id="ethical-ad-placement"
      class="flat"
      data-ea-publisher="readthedocs"
      data-ea-type="readthedocs-sidebar"
      data-ea-manual="true">
</div></div>
  </div>


      </div>
      
      <main id="main-content" class="bd-main" role="main">
        
        
          <div class="bd-content">
            <div class="bd-article-container">
              
              <div class="bd-header-article d-print-none">
<div class="header-article-items header-article__inner">
  
    <div class="header-article-items__start">
      
        <div class="header-article-item">

<nav aria-label="Breadcrumb" class="d-print-none">
  <ul class="bd-breadcrumbs">
    
    <li class="breadcrumb-item breadcrumb-home">
      <a href="../../../index.html" class="nav-link" aria-label="Home">
        <i class="fa-solid fa-home"></i>
      </a>
    </li>
    
    <li class="breadcrumb-item"><a href="../../index.html" class="nav-link">Module code</a></li>
    
    <li class="breadcrumb-item active" aria-current="page"><span class="ellipsis">sadrive.commands.manipulation</span></li>
  </ul>
</nav>
</div>
      
    </div>
  
  
</div>
</div>
              
              
              
                
<div id="searchbox"></div>
                <article class="bd-article">
                  
  <h1>Source code for sadrive.commands.manipulation</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">Provides CLI commands for common SA-Drive operations:</span>
<span class="sd">- newfolder: Create a new Drive folder and record it locally.</span>
<span class="sd">- share: Share files or folders (recursive) and update DB.</span>
<span class="sd">- rename: Rename a Drive item and update DB.</span>
<span class="sd">- open_link: Open a Drive item in the browser.</span>
<span class="sd">- details: Display storage usage table for all service accounts.</span>
<span class="sd">- search: Interactive search of files/folders by name.</span>
<span class="sd">&quot;&quot;&quot;</span>
<span class="c1"># pyright: reportUnknownMemberType=false</span>
<span class="c1"># pyright: reportAttributeAccessIssue=false</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">click</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sadrive.helpers.utils</span><span class="w"> </span><span class="kn">import</span> <span class="p">(</span>
    <span class="n">get_parent_id</span><span class="p">,</span>
    <span class="n">humanbytes</span><span class="p">,</span>
    <span class="n">MAGIC_SIZE</span><span class="p">,</span>
    <span class="n">FF</span>
<span class="p">)</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">webbrowser</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sadrive.helpers.drive</span><span class="w"> </span><span class="kn">import</span> <span class="n">SADrive</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">sadrive.helpers.dbf</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">dbf</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">click</span><span class="w"> </span><span class="kn">import</span> <span class="n">Context</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">prettytable</span><span class="w"> </span><span class="kn">import</span> <span class="n">PrettyTable</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">cast</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">inquirer</span> <span class="c1">#type:ignore</span>


<span class="nd">@click</span><span class="o">.</span><span class="n">command</span><span class="p">()</span>
<span class="nd">@click</span><span class="o">.</span><span class="n">argument</span><span class="p">(</span><span class="s2">&quot;name&quot;</span><span class="p">)</span>
<span class="nd">@click</span><span class="o">.</span><span class="n">argument</span><span class="p">(</span><span class="s2">&quot;destination&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="n">get_parent_id</span><span class="p">())</span>
<span class="k">def</span><span class="w"> </span><span class="nf">newfolder</span><span class="p">(</span><span class="n">name</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">destination</span><span class="p">:</span> <span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Create a folder in SA-Drive at the given destination ID.</span>

<span class="sd">    Args:</span>
<span class="sd">        name: Name of the new folder to create.</span>
<span class="sd">        destination: Drive folder ID under which to create (default: root).</span>

<span class="sd">    Side Effects:</span>
<span class="sd">        - Calls Drive API to create the folder.</span>
<span class="sd">        - Inserts the folder record into local DB.</span>
<span class="sd">        - Prints the new folder ID.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">fd</span> <span class="o">=</span> <span class="n">dbf</span><span class="o">.</span><span class="n">get_file_details</span><span class="p">(</span><span class="n">destination</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">destination</span> <span class="o">!=</span> <span class="n">get_parent_id</span><span class="p">():</span>
        <span class="k">if</span> <span class="p">(</span><span class="ow">not</span> <span class="n">fd</span><span class="p">)</span> <span class="ow">or</span> <span class="n">fd</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;file&quot;</span><span class="p">:</span>
            <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="s2">&quot;Destination folder id does not exist !!&quot;</span><span class="p">)</span>
            <span class="k">return</span>
    <span class="n">drive</span> <span class="o">=</span> <span class="n">SADrive</span><span class="p">(</span><span class="s1">&#39;0&#39;</span><span class="p">)</span>
    <span class="n">f</span> <span class="o">=</span> <span class="n">drive</span><span class="o">.</span><span class="n">create_folder</span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="n">destination</span><span class="p">)</span>
    <span class="n">dbf</span><span class="o">.</span><span class="n">insert_file</span><span class="p">(</span><span class="n">f</span><span class="p">,</span><span class="n">name</span><span class="p">,</span><span class="n">destination</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="s1">&#39;folder&#39;</span><span class="p">,</span><span class="s1">&#39;0&#39;</span><span class="p">,</span><span class="kc">False</span><span class="p">)</span>
    <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Folder created, folderid = </span><span class="si">{</span><span class="n">f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    

<div class="viewcode-block" id="share_file_base">
<a class="viewcode-back" href="../../../modules/sadrive.commands.manipulation.html#sadrive.commands.manipulation.share_file_base">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">share_file_base</span><span class="p">(</span><span class="n">sa_num</span><span class="p">:</span><span class="nb">str</span><span class="p">,</span><span class="n">file_id</span><span class="p">:</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Share a single file by granting reader permission to anyone.</span>

<span class="sd">    Args:</span>
<span class="sd">        sa_num: Service account number as string.</span>
<span class="sd">        file_id: Drive file or folder ID.</span>

<span class="sd">    Returns:</span>
<span class="sd">        Shareable link for the item.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">drive</span> <span class="o">=</span> <span class="n">SADrive</span><span class="p">(</span><span class="n">sa_num</span><span class="p">)</span>
    <span class="n">link</span> <span class="o">=</span> <span class="n">drive</span><span class="o">.</span><span class="n">share</span><span class="p">(</span><span class="n">file_id</span><span class="p">)</span>
    <span class="n">dbf</span><span class="o">.</span><span class="n">share_file</span><span class="p">(</span><span class="n">file_id</span><span class="p">,</span><span class="kc">True</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">link</span></div>



<div class="viewcode-block" id="share_folder_recursive">
<a class="viewcode-back" href="../../../modules/sadrive.commands.manipulation.html#sadrive.commands.manipulation.share_folder_recursive">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">share_folder_recursive</span><span class="p">(</span><span class="n">file_id</span><span class="p">:</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Recursively mark all items inside a folder as shared.</span>

<span class="sd">    Args:</span>
<span class="sd">        file_id: ID of the parent folder.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">children</span> <span class="o">=</span> <span class="n">dbf</span><span class="o">.</span><span class="n">find_children</span><span class="p">(</span><span class="n">file_id</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">child</span> <span class="ow">in</span> <span class="n">children</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">child</span><span class="p">[</span><span class="s1">&#39;type&#39;</span><span class="p">]</span> <span class="o">==</span> <span class="s1">&#39;file&#39;</span><span class="p">:</span>
            <span class="n">dbf</span><span class="o">.</span><span class="n">share_file</span><span class="p">(</span><span class="n">child</span><span class="p">[</span><span class="s1">&#39;_id&#39;</span><span class="p">],</span><span class="kc">True</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">child</span><span class="p">[</span><span class="s1">&#39;type&#39;</span><span class="p">]</span> <span class="o">==</span> <span class="s1">&#39;folder&#39;</span><span class="p">:</span>
            <span class="n">share_folder_recursive</span><span class="p">(</span><span class="n">child</span><span class="p">[</span><span class="s1">&#39;_id&#39;</span><span class="p">])</span>
    <span class="n">dbf</span><span class="o">.</span><span class="n">share_file</span><span class="p">(</span><span class="n">file_id</span><span class="p">,</span><span class="kc">True</span><span class="p">)</span></div>


<span class="nd">@click</span><span class="o">.</span><span class="n">command</span><span class="p">()</span>
<span class="nd">@click</span><span class="o">.</span><span class="n">argument</span><span class="p">(</span><span class="s2">&quot;id&quot;</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="n">get_parent_id</span><span class="p">())</span>
<span class="k">def</span><span class="w"> </span><span class="nf">share</span><span class="p">(</span><span class="nb">id</span><span class="p">:</span> <span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Share a file or folder (recursive) with anyone.</span>

<span class="sd">    Args:</span>
<span class="sd">        id: Drive item ID to share (default: root).</span>

<span class="sd">    Side Effects:</span>
<span class="sd">        - Updates sharing permissions via Drive API.</span>
<span class="sd">        - Updates local DB share flags.</span>
<span class="sd">        - Prints the shareable link.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">fd</span> <span class="o">=</span> <span class="n">dbf</span><span class="o">.</span><span class="n">get_file_details</span><span class="p">(</span><span class="nb">id</span><span class="p">)</span>
    <span class="k">if</span> <span class="p">(</span><span class="ow">not</span> <span class="n">fd</span><span class="p">):</span>
        <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="s2">&quot;Destination id does not exist !!&quot;</span><span class="p">)</span>
        <span class="k">return</span>
    <span class="k">if</span> <span class="n">fd</span><span class="p">[</span><span class="s1">&#39;type&#39;</span><span class="p">]</span> <span class="o">==</span> <span class="s1">&#39;folder&#39;</span><span class="p">:</span>
        <span class="n">share_folder_recursive</span><span class="p">(</span><span class="n">fd</span><span class="p">[</span><span class="s1">&#39;_id&#39;</span><span class="p">])</span>

    <span class="n">link</span> <span class="o">=</span> <span class="n">share_file_base</span><span class="p">(</span><span class="n">fd</span><span class="p">[</span><span class="s1">&#39;service_acc_num&#39;</span><span class="p">],</span><span class="n">fd</span><span class="p">[</span><span class="s1">&#39;_id&#39;</span><span class="p">])</span>
    <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Shared Link (anyone can view) = </span><span class="si">{</span><span class="n">link</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    

<span class="nd">@click</span><span class="o">.</span><span class="n">command</span><span class="p">()</span>
<span class="nd">@click</span><span class="o">.</span><span class="n">argument</span><span class="p">(</span><span class="s2">&quot;newname&quot;</span><span class="p">)</span>
<span class="nd">@click</span><span class="o">.</span><span class="n">argument</span><span class="p">(</span><span class="s2">&quot;id&quot;</span><span class="p">)</span>
<span class="k">def</span><span class="w"> </span><span class="nf">rename</span><span class="p">(</span><span class="n">newname</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span><span class="nb">id</span><span class="p">:</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Rename a Drive file or folder.</span>

<span class="sd">    Args:</span>
<span class="sd">        newname: New name for the item.</span>
<span class="sd">        id: Drive item ID to rename.</span>

<span class="sd">    Side Effects:</span>
<span class="sd">        - Calls Drive API to rename.</span>
<span class="sd">        - Updates local DB record.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">fd</span> <span class="o">=</span> <span class="n">dbf</span><span class="o">.</span><span class="n">get_file_details</span><span class="p">(</span><span class="nb">id</span><span class="p">)</span>
    <span class="k">if</span> <span class="p">(</span><span class="ow">not</span> <span class="n">fd</span><span class="p">):</span>
        <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="s2">&quot;Destination id does not exist !!&quot;</span><span class="p">)</span>
        <span class="k">return</span>
    <span class="n">sa_num</span> <span class="o">=</span> <span class="n">fd</span><span class="p">[</span><span class="s1">&#39;service_acc_num&#39;</span><span class="p">]</span>
    <span class="n">drive</span> <span class="o">=</span> <span class="n">SADrive</span><span class="p">(</span><span class="n">sa_num</span><span class="p">)</span>
    <span class="n">drive</span><span class="o">.</span><span class="n">rename</span><span class="p">(</span><span class="nb">id</span><span class="p">,</span><span class="n">newname</span><span class="p">)</span>
    <span class="n">dbf</span><span class="o">.</span><span class="n">rename_file</span><span class="p">(</span><span class="nb">id</span><span class="p">,</span><span class="n">newname</span><span class="p">)</span>

<span class="nd">@click</span><span class="o">.</span><span class="n">command</span><span class="p">()</span>
<span class="nd">@click</span><span class="o">.</span><span class="n">argument</span><span class="p">(</span><span class="s1">&#39;id&#39;</span><span class="p">)</span>
<span class="k">def</span><span class="w"> </span><span class="nf">open_link</span><span class="p">(</span><span class="nb">id</span><span class="p">:</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Open the given file or folder ID in the default web browser.</span>

<span class="sd">    Args:</span>
<span class="sd">        id: Drive item ID.</span>

<span class="sd">    Side Effects:</span>
<span class="sd">        - Launches browser to the Drive open URL.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">webbrowser</span><span class="o">.</span><span class="n">open</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;https://drive.google.com/open?id=</span><span class="si">{</span><span class="nb">id</span><span class="si">}</span><span class="s1">&#39;</span><span class="p">)</span>
    <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Opened https://drive.google.com/open?id=</span><span class="si">{</span><span class="nb">id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    
<span class="nd">@click</span><span class="o">.</span><span class="n">command</span><span class="p">()</span>
<span class="k">def</span><span class="w"> </span><span class="nf">details</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Display storage usage details for all service accounts.</span>

<span class="sd">    Prints a table with occupied and free space per account.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">items</span> <span class="o">=</span><span class="p">[(</span><span class="n">i</span><span class="p">[</span><span class="s1">&#39;_id&#39;</span><span class="p">],</span><span class="n">i</span><span class="p">[</span><span class="s1">&#39;size&#39;</span><span class="p">])</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">dbf</span><span class="o">.</span><span class="n">get_size_map</span><span class="p">()]</span>
    <span class="n">items</span><span class="o">.</span><span class="n">sort</span><span class="p">(</span><span class="n">key</span><span class="o">=</span><span class="k">lambda</span> <span class="n">x</span><span class="p">:</span><span class="n">x</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span><span class="n">reverse</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="n">occ</span><span class="p">,</span><span class="n">avail</span> <span class="o">=</span> <span class="n">dbf</span><span class="o">.</span><span class="n">space_details</span><span class="p">()</span>
    <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Space details are as follows:</span><span class="se">\n</span><span class="s2">Occupied:</span><span class="si">{</span><span class="n">humanbytes</span><span class="p">(</span><span class="n">occ</span><span class="p">)</span><span class="si">}</span><span class="s2"> | Available: </span><span class="si">{</span><span class="n">humanbytes</span><span class="p">(</span><span class="n">avail</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="n">table</span> <span class="o">=</span> <span class="n">PrettyTable</span><span class="p">()</span>
    <span class="n">table</span><span class="o">.</span><span class="n">field_names</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;SA number&quot;</span><span class="p">,</span> <span class="s2">&quot;Occupied&quot;</span><span class="p">,</span> <span class="s2">&quot;Free&quot;</span><span class="p">]</span>
    <span class="k">for</span> <span class="n">num</span><span class="p">,</span><span class="n">sz</span> <span class="ow">in</span> <span class="n">items</span><span class="p">:</span>
        <span class="n">table</span><span class="o">.</span><span class="n">add_row</span><span class="p">([</span><span class="n">num</span><span class="p">,</span><span class="n">humanbytes</span><span class="p">(</span><span class="n">sz</span><span class="p">),</span><span class="n">humanbytes</span><span class="p">(</span><span class="n">MAGIC_SIZE</span><span class="o">-</span><span class="n">sz</span><span class="p">)])</span>
    <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="n">table</span><span class="o">.</span><span class="n">get_string</span><span class="p">())</span>
    <span class="k">return</span>   


<div class="viewcode-block" id="search_for_file">
<a class="viewcode-back" href="../../../modules/sadrive.commands.manipulation.html#sadrive.commands.manipulation.search_for_file">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">search_for_file</span><span class="p">(</span><span class="n">file_name</span><span class="p">:</span><span class="nb">str</span><span class="p">,</span><span class="n">fuzzy</span><span class="p">:</span><span class="nb">bool</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Search for files by name, either via Drive API fuzzy search or DB substring matching.</span>

<span class="sd">    Args:</span>
<span class="sd">        file_name: The search term to match filenames against.</span>
<span class="sd">        fuzzy: If True, perform fuzzy search through Drive API; else, use database LIKE search.</span>

<span class="sd">    Returns:</span>
<span class="sd">        List of file detail dicts matching the search criteria.</span>

<span class="sd">    Behavior:</span>
<span class="sd">        - Fuzzy: Retrieves Drive files via SADrive.search, then filters those present in the DB.</span>
<span class="sd">        - Non-fuzzy: Directly queries DB for filenames containing the term.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="n">fuzzy</span><span class="p">:</span>
        <span class="n">actual</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="n">hp</span> <span class="o">=</span> <span class="n">SADrive</span><span class="p">(</span><span class="s1">&#39;0&#39;</span><span class="p">)</span>
        <span class="n">ls</span> <span class="o">=</span> <span class="n">hp</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="n">file_name</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">ls</span><span class="p">:</span>
            <span class="n">tmp</span>  <span class="o">=</span> <span class="n">dbf</span><span class="o">.</span><span class="n">get_file_details</span><span class="p">(</span><span class="n">i</span><span class="p">[</span><span class="s1">&#39;id&#39;</span><span class="p">])</span>
            <span class="k">if</span> <span class="n">tmp</span><span class="p">:</span>
                <span class="n">actual</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">tmp</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">actual</span> <span class="o">=</span> <span class="n">dbf</span><span class="o">.</span><span class="n">search_for_file_contains</span><span class="p">(</span><span class="n">file_name</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">actual</span></div>


<span class="nd">@click</span><span class="o">.</span><span class="n">command</span><span class="p">()</span>
<span class="nd">@click</span><span class="o">.</span><span class="n">argument</span><span class="p">(</span><span class="s1">&#39;name&#39;</span><span class="p">)</span>
<span class="nd">@click</span><span class="o">.</span><span class="n">argument</span><span class="p">(</span><span class="s1">&#39;fuzzy&#39;</span><span class="p">,</span><span class="n">default</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="nd">@click</span><span class="o">.</span><span class="n">pass_context</span>
<span class="k">def</span><span class="w"> </span><span class="nf">search</span><span class="p">(</span><span class="n">ctx</span><span class="p">:</span><span class="n">Context</span><span class="p">,</span><span class="n">name</span><span class="p">:</span><span class="nb">str</span><span class="p">,</span><span class="n">fuzzy</span><span class="p">:</span><span class="nb">bool</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Search the SA-Drive for files or folders by name.</span>

<span class="sd">    Args:</span>
<span class="sd">        name: Substring to search for.</span>
<span class="sd">        fuzzy: If True, use Drive API fuzzy search; else database LIKE search.</span>

<span class="sd">    Side Effects:</span>
<span class="sd">        - Prompts user to select a result interactively.</span>
<span class="sd">        - Opens the selected item in browser via `open_link`.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">data</span> <span class="o">=</span> <span class="n">search_for_file</span><span class="p">(</span><span class="n">name</span><span class="p">,</span><span class="n">fuzzy</span><span class="p">)</span>
    <span class="n">choices</span> <span class="o">=</span> <span class="p">[</span>
            <span class="n">FF</span><span class="p">(</span><span class="n">i</span><span class="p">[</span><span class="s2">&quot;file_name&quot;</span><span class="p">],</span> <span class="n">i</span><span class="p">[</span><span class="s2">&quot;_id&quot;</span><span class="p">],</span> <span class="n">i</span><span class="p">[</span><span class="s2">&quot;parent_id&quot;</span><span class="p">],</span> <span class="n">i</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">])</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">data</span>
        <span class="p">]</span>
    <span class="n">choices</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">FF</span><span class="p">(</span><span class="s1">&#39;exit&#39;</span><span class="p">,</span><span class="s1">&#39;&#39;</span><span class="p">,</span><span class="s1">&#39;&#39;</span><span class="p">,</span><span class="s1">&#39;&#39;</span><span class="p">))</span>
    <span class="n">answer</span> <span class="o">=</span> <span class="n">cast</span><span class="p">(</span><span class="nb">dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span><span class="n">FF</span><span class="p">],</span><span class="n">inquirer</span><span class="o">.</span><span class="n">prompt</span><span class="p">(</span>
            <span class="p">[</span>
                <span class="n">inquirer</span><span class="o">.</span><span class="n">List</span><span class="p">(</span>
                    <span class="s2">&quot;choice&quot;</span><span class="p">,</span> <span class="n">message</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Search results for </span><span class="si">{</span><span class="n">name</span><span class="si">}</span><span class="s2"> and </span><span class="si">{</span><span class="n">fuzzy</span><span class="si">=}</span><span class="s2">. Press enter on a choice to open in browser!&quot;</span><span class="p">,</span> <span class="n">choices</span><span class="o">=</span><span class="n">choices</span>
                <span class="p">)</span>
            <span class="p">]</span>
        <span class="p">))</span>
    <span class="n">choice</span> <span class="o">=</span> <span class="n">answer</span><span class="p">[</span><span class="s2">&quot;choice&quot;</span><span class="p">]</span>
    <span class="k">if</span> <span class="n">choice</span><span class="o">.</span><span class="n">name</span> <span class="o">==</span> <span class="s1">&#39;exit&#39;</span><span class="p">:</span>
        <span class="k">return</span>
    <span class="n">ctx</span><span class="o">.</span><span class="n">invoke</span><span class="p">(</span><span class="n">open_link</span><span class="p">,</span><span class="nb">id</span><span class="o">=</span><span class="n">choice</span><span class="o">.</span><span class="n">file_id</span><span class="p">)</span>
    
</pre></div>

                </article>
              
              
              
              
              
                <footer class="prev-next-footer d-print-none">
                  
<div class="prev-next-area">
</div>
                </footer>
              
            </div>
            
            
              
            
          </div>
          <footer class="bd-footer-content">
            
          </footer>
        
      </main>
    </div>
  </div>
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script defer src="../../../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf"></script>
<script defer src="../../../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf"></script>

  <footer class="bd-footer">
<div class="bd-footer__inner bd-page-width">
  
    <div class="footer-items__start">
      
        <div class="footer-item">

  <p class="copyright">
    
      © Copyright 2025, jsmaskeen.
      <br/>
    
  </p>
</div>
      
        <div class="footer-item">

  <p class="sphinx-version">
    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    <br/>
  </p>
</div>
      
    </div>
  
  
  
    <div class="footer-items__end">
      
        <div class="footer-item">
<p class="theme-version">
  <!-- # L10n: Setting the PST URL as an argument as this does not need to be localized -->
  Built with the <a href="https://pydata-sphinx-theme.readthedocs.io/en/stable/index.html">PyData Sphinx Theme</a> 0.16.1.
</p></div>
      
    </div>
  
</div>

  </footer>
  </body>
</html>