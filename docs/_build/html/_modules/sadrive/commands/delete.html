
<!DOCTYPE html>


<html lang="en" data-content_root="../../../" >

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>sadrive.commands.delete &#8212; sadrive 2.0.0 documentation</title>
  
  
  
  <script data-cfasync="false">
    document.documentElement.dataset.mode = localStorage.getItem("mode") || "";
    document.documentElement.dataset.theme = localStorage.getItem("theme") || "";
  </script>
  <!--
    this give us a css class that will be invisible only if js is disabled
  -->
  <noscript>
    <style>
      .pst-js-only { display: none !important; }

    </style>
  </noscript>
  
  <!-- Loaded before other Sphinx assets -->
  <link href="../../../_static/styles/theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />
<link href="../../../_static/styles/pydata-sphinx-theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />

    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=8f2a1f02" />
  
  <!-- So that users can add custom icons -->
  <script src="../../../_static/scripts/fontawesome.js?digest=8878045cc6db502f8baf"></script>
  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="../../../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf" />
<link rel="preload" as="script" href="../../../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf" />

    <script src="../../../_static/documentation_options.js?v=51b770b3"></script>
    <script src="../../../_static/doctools.js?v=9bcbadda"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script>DOCUMENTATION_OPTIONS.pagename = '_modules/sadrive/commands/delete';</script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
  <meta name="viewport" content="width=device-width, initial-scale=1"/>
  <meta name="docsearch:language" content="en"/>
  <meta name="docsearch:version" content="" />
  </head>
  
  
  <body data-bs-spy="scroll" data-bs-target=".bd-toc-nav" data-offset="180" data-bs-root-margin="0px 0px -60%" data-default-mode="">

  
  
  <div id="pst-skip-link" class="skip-link d-print-none"><a href="#main-content">Skip to main content</a></div>
  
  <div id="pst-scroll-pixel-helper"></div>
  
  <button type="button" class="btn rounded-pill" id="pst-back-to-top">
    <i class="fa-solid fa-arrow-up"></i>Back to top</button>

  
  <dialog id="pst-search-dialog">
    
<form class="bd-search d-flex align-items-center"
      action="../../../search.html"
      method="get">
  <i class="fa-solid fa-magnifying-glass"></i>
  <input type="search"
         class="form-control"
         name="q"
         placeholder="Search the docs ..."
         aria-label="Search the docs ..."
         autocomplete="off"
         autocorrect="off"
         autocapitalize="off"
         spellcheck="false"/>
  <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd>K</kbd></span>
</form>
  </dialog>

  <div class="pst-async-banner-revealer d-none">
  <aside id="bd-header-version-warning" class="d-none d-print-none" aria-label="Version warning"></aside>
</div>

  
    <header class="bd-header navbar navbar-expand-lg bd-navbar d-print-none">
<div class="bd-header__inner bd-page-width">
  <button class="pst-navbar-icon sidebar-toggle primary-toggle" aria-label="Site navigation">
    <span class="fa-solid fa-bars"></span>
  </button>
  
  
  <div class="col-lg-3 navbar-header-items__start">
    
      <div class="navbar-item">

  
    
  

<a class="navbar-brand logo" href="../../../index.html">
  
  
  
  
  
  
    <p class="title logo__title">sadrive 2.0.0 documentation</p>
  
</a></div>
    
  </div>
  
  <div class="col-lg-9 navbar-header-items">
    
    <div class="me-auto navbar-header-items__center">
      
        <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
      
    </div>
    
    
    <div class="navbar-header-items__end">
      
        <div class="navbar-item navbar-persistent--container">
          

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
        </div>
      
      
        <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
      
    </div>
    
  </div>
  
  
    <div class="navbar-persistent--mobile">

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
    </div>
  

  
</div>

    </header>
  

  <div class="bd-container">
    <div class="bd-container__inner bd-page-width">
      
      
      
        
      
      <dialog id="pst-primary-sidebar-modal"></dialog>
      <div id="pst-primary-sidebar" class="bd-sidebar-primary bd-sidebar hide-on-wide">
        

  
  <div class="sidebar-header-items sidebar-primary__section">
    
    
      <div class="sidebar-header-items__center">
        
          
          
            <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
          
        
      </div>
    
    
    
      <div class="sidebar-header-items__end">
        
          <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
        
      </div>
    
  </div>
  
  
  <div class="sidebar-primary-items__end sidebar-primary__section">
      <div class="sidebar-primary-item">
<div id="ethical-ad-placement"
      class="flat"
      data-ea-publisher="readthedocs"
      data-ea-type="readthedocs-sidebar"
      data-ea-manual="true">
</div></div>
  </div>


      </div>
      
      <main id="main-content" class="bd-main" role="main">
        
        
          <div class="bd-content">
            <div class="bd-article-container">
              
              <div class="bd-header-article d-print-none">
<div class="header-article-items header-article__inner">
  
    <div class="header-article-items__start">
      
        <div class="header-article-item">

<nav aria-label="Breadcrumb" class="d-print-none">
  <ul class="bd-breadcrumbs">
    
    <li class="breadcrumb-item breadcrumb-home">
      <a href="../../../index.html" class="nav-link" aria-label="Home">
        <i class="fa-solid fa-home"></i>
      </a>
    </li>
    
    <li class="breadcrumb-item"><a href="../../index.html" class="nav-link">Module code</a></li>
    
    <li class="breadcrumb-item active" aria-current="page"><span class="ellipsis">sadrive.commands.delete</span></li>
  </ul>
</nav>
</div>
      
    </div>
  
  
</div>
</div>
              
              
              
                
<div id="searchbox"></div>
                <article class="bd-article">
                  
  <h1>Source code for sadrive.commands.delete</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">Provides CLI commands for deleting files and folders from Google Drive</span>
<span class="sd">and updating the local database accordingly. Supports recursive folder deletion</span>
<span class="sd">and full cleanup of all service-account drives.</span>

<span class="sd">Functions:</span>
<span class="sd">- del_file: Remove a single file from Drive and database.</span>
<span class="sd">- del_folder: Recursively delete a folder and its contents.</span>

<span class="sd">Commands:</span>
<span class="sd">- delete(fileid): Delete a specific file or folder by ID.</span>
<span class="sd">- clearall(): Wipe all files across all service accounts and reset DB.</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="c1"># pyright: reportUnknownMemberType=false</span>
<span class="c1"># pyright: reportAttributeAccessIssue=false</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">click</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sadrive.helpers.drive</span><span class="w"> </span><span class="kn">import</span> <span class="n">SADrive</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">sadrive.helpers.dbf</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">dbf</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">concurrent.futures</span><span class="w"> </span><span class="kn">import</span> <span class="n">ThreadPoolExecutor</span><span class="p">,</span> <span class="n">as_completed</span>


<div class="viewcode-block" id="del_file">
<a class="viewcode-back" href="../../../modules/sadrive.commands.delete.html#sadrive.commands.delete.del_file">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">del_file</span><span class="p">(</span><span class="n">sa_num</span><span class="p">:</span><span class="nb">str</span><span class="p">,</span><span class="n">file_id</span><span class="p">:</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Deletes a single file from Google Drive and removes its record from the DB.</span>

<span class="sd">    Args:</span>
<span class="sd">        sa_num: Service account number owning the file.</span>
<span class="sd">        file_id: Drive file ID to delete.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">drive</span> <span class="o">=</span> <span class="n">SADrive</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">sa_num</span><span class="p">))</span>
    <span class="n">drive</span><span class="o">.</span><span class="n">delete_file</span><span class="p">(</span><span class="n">file_id</span><span class="p">)</span>
    <span class="n">dbf</span><span class="o">.</span><span class="n">delete_file</span><span class="p">(</span><span class="n">file_id</span><span class="p">)</span></div>

    
<div class="viewcode-block" id="del_folder">
<a class="viewcode-back" href="../../../modules/sadrive.commands.delete.html#sadrive.commands.delete.del_folder">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">del_folder</span><span class="p">(</span><span class="n">sa_num</span><span class="p">:</span><span class="nb">str</span><span class="p">,</span><span class="n">file_id</span><span class="p">:</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Recursively deletes all contents of a folder in Drive and the database,</span>
<span class="sd">    then deletes the folder itself.</span>

<span class="sd">    Args:</span>
<span class="sd">        sa_num: Service account number owning the folder.</span>
<span class="sd">        file_id: Drive folder ID to delete.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">children</span> <span class="o">=</span> <span class="n">dbf</span><span class="o">.</span><span class="n">find_children</span><span class="p">(</span><span class="n">file_id</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">child</span> <span class="ow">in</span> <span class="n">children</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">child</span><span class="p">[</span><span class="s1">&#39;type&#39;</span><span class="p">]</span> <span class="o">==</span> <span class="s1">&#39;file&#39;</span><span class="p">:</span>
            <span class="n">del_file</span><span class="p">(</span><span class="n">child</span><span class="p">[</span><span class="s1">&#39;service_acc_num&#39;</span><span class="p">],</span><span class="n">child</span><span class="p">[</span><span class="s1">&#39;_id&#39;</span><span class="p">])</span>
        <span class="k">elif</span> <span class="n">child</span><span class="p">[</span><span class="s1">&#39;type&#39;</span><span class="p">]</span> <span class="o">==</span> <span class="s1">&#39;folder&#39;</span><span class="p">:</span>
            <span class="n">del_folder</span><span class="p">(</span><span class="n">child</span><span class="p">[</span><span class="s1">&#39;service_acc_num&#39;</span><span class="p">],</span><span class="n">child</span><span class="p">[</span><span class="s1">&#39;_id&#39;</span><span class="p">])</span>
    <span class="n">del_file</span><span class="p">(</span><span class="n">sa_num</span><span class="p">,</span><span class="n">file_id</span><span class="p">)</span></div>


<span class="nd">@click</span><span class="o">.</span><span class="n">command</span><span class="p">()</span>
<span class="nd">@click</span><span class="o">.</span><span class="n">argument</span><span class="p">(</span><span class="s2">&quot;fileid&quot;</span><span class="p">)</span>
<span class="k">def</span><span class="w"> </span><span class="nf">delete</span><span class="p">(</span><span class="n">fileid</span><span class="p">:</span> <span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CLI command to delete a file or folder by its ID.</span>

<span class="sd">    If the ID corresponds to a folder, deletion is recursive.</span>

<span class="sd">    Args:</span>
<span class="sd">        fileid: The Drive ID of the file or folder.</span>

<span class="sd">    Side Effects:</span>
<span class="sd">        - Removes the file/folder from Google Drive.</span>
<span class="sd">        - Updates the local database to reflect deletion.</span>
<span class="sd">        - Prints status messages to the console.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">fd</span> <span class="o">=</span> <span class="n">dbf</span><span class="o">.</span><span class="n">get_file_details</span><span class="p">(</span><span class="n">fileid</span><span class="p">)</span>
    <span class="k">if</span> <span class="p">(</span><span class="ow">not</span> <span class="n">fd</span><span class="p">):</span>
        <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="s2">&quot;Destination folder/file id does not exist !!&quot;</span><span class="p">)</span>
        <span class="k">return</span>
    <span class="k">if</span> <span class="n">fd</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;file&quot;</span><span class="p">:</span>
        <span class="n">del_file</span><span class="p">(</span><span class="n">fd</span><span class="p">[</span><span class="s1">&#39;service_acc_num&#39;</span><span class="p">],</span><span class="n">fileid</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">del_folder</span><span class="p">(</span><span class="n">fd</span><span class="p">[</span><span class="s1">&#39;service_acc_num&#39;</span><span class="p">],</span><span class="n">fileid</span><span class="p">)</span>
        
<span class="nd">@click</span><span class="o">.</span><span class="n">command</span><span class="p">()</span>
<span class="k">def</span><span class="w"> </span><span class="nf">clearall</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CLI command to permanently delete all files across all service accounts</span>
<span class="sd">    and reset the local file and size maps.</span>

<span class="sd">    Side Effects:</span>
<span class="sd">        - Deletes every non-trashed file owned by each service account.</span>
<span class="sd">        - Clears the file_map table.</span>
<span class="sd">        - Resets all service account sizes to zero.</span>
<span class="sd">        - Prints progress and a final confirmation message.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">drives</span> <span class="o">=</span> <span class="p">[</span><span class="n">SADrive</span><span class="p">(</span><span class="n">i</span><span class="p">[</span><span class="s1">&#39;_id&#39;</span><span class="p">])</span> <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">dbf</span><span class="o">.</span><span class="n">get_size_map</span><span class="p">()]</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">delete_all</span><span class="p">(</span><span class="n">drive</span><span class="p">:</span><span class="n">SADrive</span><span class="p">):</span>
        <span class="n">drive</span><span class="o">.</span><span class="n">delete_all_files</span><span class="p">()</span>
        <span class="k">return</span>
    
    <span class="k">with</span> <span class="n">ThreadPoolExecutor</span><span class="p">(</span><span class="n">max_workers</span><span class="o">=</span><span class="mi">5</span><span class="p">)</span> <span class="k">as</span> <span class="n">executor</span><span class="p">:</span>
        <span class="n">futures</span> <span class="o">=</span> <span class="p">[</span><span class="n">executor</span><span class="o">.</span><span class="n">submit</span><span class="p">(</span><span class="n">delete_all</span><span class="p">,</span> <span class="n">d</span><span class="p">)</span> <span class="k">for</span> <span class="n">d</span> <span class="ow">in</span> <span class="n">drives</span><span class="p">]</span>
        <span class="k">for</span> <span class="n">future</span> <span class="ow">in</span> <span class="n">as_completed</span><span class="p">(</span><span class="n">futures</span><span class="p">):</span>
            <span class="n">future</span><span class="o">.</span><span class="n">result</span><span class="p">()</span>
    <span class="n">dbf</span><span class="o">.</span><span class="n">clear_file_map</span><span class="p">()</span>
    <span class="n">dbf</span><span class="o">.</span><span class="n">reset_sa_sizes</span><span class="p">()</span>
    <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="s2">&quot;SA Drive has been reset.&quot;</span><span class="p">)</span>
            
</pre></div>

                </article>
              
              
              
              
              
                <footer class="prev-next-footer d-print-none">
                  
<div class="prev-next-area">
</div>
                </footer>
              
            </div>
            
            
              
            
          </div>
          <footer class="bd-footer-content">
            
          </footer>
        
      </main>
    </div>
  </div>
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script defer src="../../../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf"></script>
<script defer src="../../../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf"></script>

  <footer class="bd-footer">
<div class="bd-footer__inner bd-page-width">
  
    <div class="footer-items__start">
      
        <div class="footer-item">

  <p class="copyright">
    
      © Copyright 2025, jsmaskeen.
      <br/>
    
  </p>
</div>
      
        <div class="footer-item">

  <p class="sphinx-version">
    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    <br/>
  </p>
</div>
      
    </div>
  
  
  
    <div class="footer-items__end">
      
        <div class="footer-item">
<p class="theme-version">
  <!-- # L10n: Setting the PST URL as an argument as this does not need to be localized -->
  Built with the <a href="https://pydata-sphinx-theme.readthedocs.io/en/stable/index.html">PyData Sphinx Theme</a> 0.16.1.
</p></div>
      
    </div>
  
</div>

  </footer>
  </body>
</html>