
<!DOCTYPE html>


<html lang="en" data-content_root="../../../" >

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>sadrive.commands.navigate &#8212; sadrive 2.0.0 documentation</title>
  
  
  
  <script data-cfasync="false">
    document.documentElement.dataset.mode = localStorage.getItem("mode") || "";
    document.documentElement.dataset.theme = localStorage.getItem("theme") || "";
  </script>
  <!--
    this give us a css class that will be invisible only if js is disabled
  -->
  <noscript>
    <style>
      .pst-js-only { display: none !important; }

    </style>
  </noscript>
  
  <!-- Loaded before other Sphinx assets -->
  <link href="../../../_static/styles/theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />
<link href="../../../_static/styles/pydata-sphinx-theme.css?digest=8878045cc6db502f8baf" rel="stylesheet" />

    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=8f2a1f02" />
  
  <!-- So that users can add custom icons -->
  <script src="../../../_static/scripts/fontawesome.js?digest=8878045cc6db502f8baf"></script>
  <!-- Pre-loaded scripts that we'll load fully later -->
  <link rel="preload" as="script" href="../../../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf" />
<link rel="preload" as="script" href="../../../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf" />

    <script src="../../../_static/documentation_options.js?v=51b770b3"></script>
    <script src="../../../_static/doctools.js?v=9bcbadda"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script>DOCUMENTATION_OPTIONS.pagename = '_modules/sadrive/commands/navigate';</script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
  <meta name="viewport" content="width=device-width, initial-scale=1"/>
  <meta name="docsearch:language" content="en"/>
  <meta name="docsearch:version" content="" />
  </head>
  
  
  <body data-bs-spy="scroll" data-bs-target=".bd-toc-nav" data-offset="180" data-bs-root-margin="0px 0px -60%" data-default-mode="">

  
  
  <div id="pst-skip-link" class="skip-link d-print-none"><a href="#main-content">Skip to main content</a></div>
  
  <div id="pst-scroll-pixel-helper"></div>
  
  <button type="button" class="btn rounded-pill" id="pst-back-to-top">
    <i class="fa-solid fa-arrow-up"></i>Back to top</button>

  
  <dialog id="pst-search-dialog">
    
<form class="bd-search d-flex align-items-center"
      action="../../../search.html"
      method="get">
  <i class="fa-solid fa-magnifying-glass"></i>
  <input type="search"
         class="form-control"
         name="q"
         placeholder="Search the docs ..."
         aria-label="Search the docs ..."
         autocomplete="off"
         autocorrect="off"
         autocapitalize="off"
         spellcheck="false"/>
  <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd>K</kbd></span>
</form>
  </dialog>

  <div class="pst-async-banner-revealer d-none">
  <aside id="bd-header-version-warning" class="d-none d-print-none" aria-label="Version warning"></aside>
</div>

  
    <header class="bd-header navbar navbar-expand-lg bd-navbar d-print-none">
<div class="bd-header__inner bd-page-width">
  <button class="pst-navbar-icon sidebar-toggle primary-toggle" aria-label="Site navigation">
    <span class="fa-solid fa-bars"></span>
  </button>
  
  
  <div class="col-lg-3 navbar-header-items__start">
    
      <div class="navbar-item">

  
    
  

<a class="navbar-brand logo" href="../../../index.html">
  
  
  
  
  
  
    <p class="title logo__title">sadrive 2.0.0 documentation</p>
  
</a></div>
    
  </div>
  
  <div class="col-lg-9 navbar-header-items">
    
    <div class="me-auto navbar-header-items__center">
      
        <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
      
    </div>
    
    
    <div class="navbar-header-items__end">
      
        <div class="navbar-item navbar-persistent--container">
          

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
        </div>
      
      
        <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
      
    </div>
    
  </div>
  
  
    <div class="navbar-persistent--mobile">

<button class="btn search-button-field search-button__button pst-js-only" title="Search" aria-label="Search" data-bs-placement="bottom" data-bs-toggle="tooltip">
 <i class="fa-solid fa-magnifying-glass"></i>
 <span class="search-button__default-text">Search</span>
 <span class="search-button__kbd-shortcut"><kbd class="kbd-shortcut__modifier">Ctrl</kbd>+<kbd class="kbd-shortcut__modifier">K</kbd></span>
</button>
    </div>
  

  
</div>

    </header>
  

  <div class="bd-container">
    <div class="bd-container__inner bd-page-width">
      
      
      
        
      
      <dialog id="pst-primary-sidebar-modal"></dialog>
      <div id="pst-primary-sidebar" class="bd-sidebar-primary bd-sidebar hide-on-wide">
        

  
  <div class="sidebar-header-items sidebar-primary__section">
    
    
      <div class="sidebar-header-items__center">
        
          
          
            <div class="navbar-item">
<nav>
  <ul class="bd-navbar-elements navbar-nav">
    
<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../interface.html">
    Command-Line Interface
  </a>
</li>


<li class="nav-item ">
  <a class="nav-link nav-internal" href="../../../apireference.html">
    API Reference
  </a>
</li>

  </ul>
</nav></div>
          
        
      </div>
    
    
    
      <div class="sidebar-header-items__end">
        
          <div class="navbar-item">

<button class="btn btn-sm nav-link pst-navbar-icon theme-switch-button pst-js-only" aria-label="Color mode" data-bs-title="Color mode"  data-bs-placement="bottom" data-bs-toggle="tooltip">
  <i class="theme-switch fa-solid fa-sun                fa-lg" data-mode="light" title="Light"></i>
  <i class="theme-switch fa-solid fa-moon               fa-lg" data-mode="dark"  title="Dark"></i>
  <i class="theme-switch fa-solid fa-circle-half-stroke fa-lg" data-mode="auto"  title="System Settings"></i>
</button></div>
        
      </div>
    
  </div>
  
  
  <div class="sidebar-primary-items__end sidebar-primary__section">
      <div class="sidebar-primary-item">
<div id="ethical-ad-placement"
      class="flat"
      data-ea-publisher="readthedocs"
      data-ea-type="readthedocs-sidebar"
      data-ea-manual="true">
</div></div>
  </div>


      </div>
      
      <main id="main-content" class="bd-main" role="main">
        
        
          <div class="bd-content">
            <div class="bd-article-container">
              
              <div class="bd-header-article d-print-none">
<div class="header-article-items header-article__inner">
  
    <div class="header-article-items__start">
      
        <div class="header-article-item">

<nav aria-label="Breadcrumb" class="d-print-none">
  <ul class="bd-breadcrumbs">
    
    <li class="breadcrumb-item breadcrumb-home">
      <a href="../../../index.html" class="nav-link" aria-label="Home">
        <i class="fa-solid fa-home"></i>
      </a>
    </li>
    
    <li class="breadcrumb-item"><a href="../../index.html" class="nav-link">Module code</a></li>
    
    <li class="breadcrumb-item active" aria-current="page"><span class="ellipsis">sadrive.commands.navigate</span></li>
  </ul>
</nav>
</div>
      
    </div>
  
  
</div>
</div>
              
              
              
                
<div id="searchbox"></div>
                <article class="bd-article">
                  
  <h1>Source code for sadrive.commands.navigate</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">Provides interactive navigation and mounting commands for SA-Drive:</span>
<span class="sd">- navigate: Traverse the folder hierarchy in the terminal.</span>
<span class="sd">- mount: Mount the SA-Drive remote locally using gclone.</span>
<span class="sd">&quot;&quot;&quot;</span>
<span class="c1"># pyright: reportUnknownMemberType=false</span>
<span class="c1"># pyright: reportAttributeAccessIssue=false</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">click</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sadrive.helpers.utils</span><span class="w"> </span><span class="kn">import</span> <span class="n">get_parent_id</span><span class="p">,</span><span class="n">FF</span><span class="p">,</span><span class="n">get_gclone_exe</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">sadrive.helpers.dbf</span><span class="w"> </span><span class="k">as</span><span class="w"> </span><span class="nn">dbf</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">List</span><span class="p">,</span><span class="n">cast</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">inquirer</span> <span class="c1">#type:ignore</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">subprocess</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">re</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">os</span>


<div class="viewcode-block" id="navigatehelp">
<a class="viewcode-back" href="../../../modules/sadrive.commands.navigate.html#sadrive.commands.navigate.navigatehelp">[docs]</a>
<span class="k">def</span><span class="w"> </span><span class="nf">navigatehelp</span><span class="p">(</span><span class="n">parent_id</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">path</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Helper function for navigating through SA-Drive folders.</span>

<span class="sd">    Presents an interactive list of current folder&#39;s children and handles</span>
<span class="sd">    user selection to move into subfolders, go back, or exit.</span>

<span class="sd">    Args:</span>
<span class="sd">        parent_id: Drive folder ID to list children.</span>
<span class="sd">        path: Accumulated path segments for display.</span>

<span class="sd">    Returns:</span>
<span class="sd">        &#39;exit&#39; to signal termination, None to continue navigation.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="n">choices</span> <span class="o">=</span> <span class="p">[</span>
            <span class="n">FF</span><span class="p">(</span><span class="n">i</span><span class="p">[</span><span class="s2">&quot;file_name&quot;</span><span class="p">],</span> <span class="n">i</span><span class="p">[</span><span class="s2">&quot;_id&quot;</span><span class="p">],</span> <span class="n">i</span><span class="p">[</span><span class="s2">&quot;parent_id&quot;</span><span class="p">],</span> <span class="n">i</span><span class="p">[</span><span class="s2">&quot;type&quot;</span><span class="p">])</span>
            <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="n">dbf</span><span class="o">.</span><span class="n">find_children</span><span class="p">(</span><span class="n">parent_id</span><span class="p">)</span>
        <span class="p">]</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">path</span><span class="p">)</span> <span class="o">!=</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">choices</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">FF</span><span class="p">(</span><span class="s2">&quot;.. (back)&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="n">parent_id</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">))</span>
        <span class="n">choices</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">FF</span><span class="p">(</span><span class="s2">&quot;exit&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">))</span>

        <span class="n">answer</span> <span class="o">=</span> <span class="n">cast</span><span class="p">(</span><span class="nb">dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span><span class="n">FF</span><span class="p">],</span><span class="n">inquirer</span><span class="o">.</span><span class="n">prompt</span><span class="p">(</span>
            <span class="p">[</span>
                <span class="n">inquirer</span><span class="o">.</span><span class="n">List</span><span class="p">(</span>
                    <span class="s2">&quot;choice&quot;</span><span class="p">,</span> <span class="n">message</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Current: /</span><span class="si">{</span><span class="s1">&#39;/&#39;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">path</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">choices</span><span class="o">=</span><span class="n">choices</span>
                <span class="p">)</span>
            <span class="p">]</span>
        <span class="p">))</span>
        <span class="n">choice</span> <span class="o">=</span> <span class="n">answer</span><span class="p">[</span><span class="s2">&quot;choice&quot;</span><span class="p">]</span>

        <span class="k">if</span> <span class="n">choice</span><span class="o">.</span><span class="n">name</span> <span class="o">==</span> <span class="s2">&quot;exit&quot;</span><span class="p">:</span>
            <span class="k">return</span> <span class="s2">&quot;exit&quot;</span>  
        <span class="k">elif</span> <span class="n">choice</span><span class="o">.</span><span class="n">name</span> <span class="o">==</span> <span class="s2">&quot;.. (back)&quot;</span><span class="p">:</span>
            <span class="n">path</span><span class="o">.</span><span class="n">pop</span><span class="p">()</span>
            <span class="k">return</span>  
        <span class="k">elif</span> <span class="n">choice</span><span class="o">.</span><span class="n">type</span> <span class="o">==</span> <span class="s1">&#39;folder&#39;</span><span class="p">:</span>
            <span class="n">path</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">choice</span><span class="o">.</span><span class="n">name</span><span class="p">)</span>
            <span class="n">result</span> <span class="o">=</span> <span class="n">navigatehelp</span><span class="p">(</span><span class="n">choice</span><span class="o">.</span><span class="n">file_id</span><span class="p">,</span> <span class="n">path</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">result</span> <span class="o">==</span> <span class="s2">&quot;exit&quot;</span><span class="p">:</span>
                <span class="k">return</span> <span class="s2">&quot;exit&quot;</span> 
        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># click.echo(f&quot;Selected {&#39;file&#39; if choice.type == &#39;file&#39; else &#39;folder&#39;}: {choice}, {&#39;file&#39; if choice.type == &#39;file&#39; else &#39;folder&#39;} id: {choice.file_id}&quot;)</span>
            <span class="k">return</span> <span class="s2">&quot;exit&quot;</span></div>


<span class="nd">@click</span><span class="o">.</span><span class="n">command</span><span class="p">()</span>
<span class="nd">@click</span><span class="o">.</span><span class="n">argument</span><span class="p">(</span><span class="s1">&#39;folderid&#39;</span><span class="p">,</span><span class="n">default</span><span class="o">=</span><span class="n">get_parent_id</span><span class="p">())</span>
<span class="k">def</span><span class="w"> </span><span class="nf">navigate</span><span class="p">(</span><span class="n">folderid</span><span class="p">:</span><span class="nb">str</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    CLI command to launch interactive navigation of SA-Drive.</span>

<span class="sd">    Args:</span>
<span class="sd">        folderid: Starting Drive folder ID (defaults to root).</span>

<span class="sd">    Behavior:</span>
<span class="sd">        Loops until user selects &#39;exit&#39;. Uses navigatehelp to traverse hierarchy.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">navigatehelp</span><span class="p">(</span><span class="n">folderid</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">result</span> <span class="o">==</span> <span class="s2">&quot;exit&quot;</span><span class="p">:</span>
            <span class="k">break</span>
    <span class="k">return</span>

<span class="nd">@click</span><span class="o">.</span><span class="n">command</span><span class="p">()</span>
<span class="k">def</span><span class="w"> </span><span class="nf">mount</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Mount the SA-Drive remote locally using the gclone tool.</span>

<span class="sd">    Detects the assigned drive letter, opens Explorer, and keeps the mount</span>
<span class="sd">    until user interrupts (Ctrl+C).</span>

<span class="sd">    Side Effects:</span>
<span class="sd">        - Runs &#39;gclone mount&#39; subprocess with vfs caching options.</span>
<span class="sd">        - Streams stderr to console to detect mount point.</span>
<span class="sd">        - Opens a file browser at the mounted drive letter.</span>
<span class="sd">        - Terminates on user interrupt.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">gclone_exe</span> <span class="o">=</span> <span class="nb">str</span><span class="p">(</span><span class="n">get_gclone_exe</span><span class="p">())</span>
    <span class="n">cmd</span> <span class="o">=</span> <span class="p">[</span>
        <span class="n">gclone_exe</span><span class="p">,</span>
        <span class="s2">&quot;mount&quot;</span><span class="p">,</span>
        <span class="s2">&quot;sadrive:&quot;</span><span class="p">,</span>
        <span class="s2">&quot;*&quot;</span><span class="p">,</span>
        <span class="s2">&quot;--read-only&quot;</span><span class="p">,</span>
        <span class="s2">&quot;--vfs-cache-mode&quot;</span><span class="p">,</span> <span class="s2">&quot;full&quot;</span><span class="p">,</span>
        <span class="s2">&quot;--vfs-cache-max-size&quot;</span><span class="p">,</span> <span class="s2">&quot;1G&quot;</span><span class="p">,</span>
        <span class="s2">&quot;--vfs-cache-max-age&quot;</span><span class="p">,</span> <span class="s2">&quot;12h&quot;</span><span class="p">,</span>
        <span class="s2">&quot;--vfs-read-chunk-size&quot;</span><span class="p">,</span> <span class="s2">&quot;64M&quot;</span><span class="p">,</span>
        <span class="s2">&quot;--vfs-read-chunk-size-limit&quot;</span><span class="p">,</span> <span class="s2">&quot;500M&quot;</span>
    <span class="p">]</span>
    <span class="n">proc</span> <span class="o">=</span> <span class="n">subprocess</span><span class="o">.</span><span class="n">Popen</span><span class="p">(</span><span class="n">cmd</span><span class="p">,</span> <span class="n">stderr</span><span class="o">=</span><span class="n">subprocess</span><span class="o">.</span><span class="n">PIPE</span><span class="p">,</span> <span class="n">text</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="n">drive_letter</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="k">for</span> <span class="n">line</span> <span class="ow">in</span> <span class="n">proc</span><span class="o">.</span><span class="n">stderr</span><span class="p">:</span> <span class="c1">#type:ignore</span>
        <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="n">line</span><span class="o">.</span><span class="n">rstrip</span><span class="p">())</span>
        <span class="n">m</span> <span class="o">=</span> <span class="n">re</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;Assigning drive letter\s+&quot;([A-Z]:)&quot;&#39;</span><span class="p">,</span> <span class="n">line</span><span class="p">)</span>
        <span class="k">if</span> <span class="n">m</span><span class="p">:</span>
            <span class="n">drive_letter</span> <span class="o">=</span> <span class="n">m</span><span class="o">.</span><span class="n">group</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
            <span class="k">break</span>
    
    <span class="k">if</span> <span class="n">drive_letter</span><span class="p">:</span>
        <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Mounted on </span><span class="si">{</span><span class="n">drive_letter</span><span class="si">}</span><span class="s2"> - launching Explorer… Press Ctrl+C to stop!&quot;</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">proc</span><span class="o">.</span><span class="n">terminate</span><span class="p">()</span>
        <span class="k">return</span>
    
    
    <span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">20</span><span class="p">):</span>           
        <span class="k">if</span> <span class="n">os</span><span class="o">.</span><span class="n">path</span><span class="o">.</span><span class="n">isdir</span><span class="p">(</span><span class="n">drive_letter</span><span class="p">):</span>
            <span class="k">break</span>
        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Unable to open explorer, open manually&quot;</span><span class="p">)</span>
        <span class="n">proc</span><span class="o">.</span><span class="n">terminate</span><span class="p">()</span>
        <span class="k">return</span>
    <span class="n">subprocess</span><span class="o">.</span><span class="n">Popen</span><span class="p">([</span><span class="s2">&quot;explorer&quot;</span><span class="p">,</span> <span class="n">drive_letter</span><span class="p">])</span>
    
    <span class="k">try</span><span class="p">:</span>
        <span class="n">proc</span><span class="o">.</span><span class="n">wait</span><span class="p">()</span>
    <span class="k">except</span> <span class="ne">KeyboardInterrupt</span><span class="p">:</span>
        <span class="n">click</span><span class="o">.</span><span class="n">echo</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span class="s2">Received Ctrl+C, unmounting…&quot;</span><span class="p">)</span>
        <span class="n">proc</span><span class="o">.</span><span class="n">terminate</span><span class="p">()</span>
</pre></div>

                </article>
              
              
              
              
              
                <footer class="prev-next-footer d-print-none">
                  
<div class="prev-next-area">
</div>
                </footer>
              
            </div>
            
            
              
            
          </div>
          <footer class="bd-footer-content">
            
          </footer>
        
      </main>
    </div>
  </div>
  
  <!-- Scripts loaded after <body> so the DOM is not blocked -->
  <script defer src="../../../_static/scripts/bootstrap.js?digest=8878045cc6db502f8baf"></script>
<script defer src="../../../_static/scripts/pydata-sphinx-theme.js?digest=8878045cc6db502f8baf"></script>

  <footer class="bd-footer">
<div class="bd-footer__inner bd-page-width">
  
    <div class="footer-items__start">
      
        <div class="footer-item">

  <p class="copyright">
    
      © Copyright 2025, jsmaskeen.
      <br/>
    
  </p>
</div>
      
        <div class="footer-item">

  <p class="sphinx-version">
    Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 8.2.3.
    <br/>
  </p>
</div>
      
    </div>
  
  
  
    <div class="footer-items__end">
      
        <div class="footer-item">
<p class="theme-version">
  <!-- # L10n: Setting the PST URL as an argument as this does not need to be localized -->
  Built with the <a href="https://pydata-sphinx-theme.readthedocs.io/en/stable/index.html">PyData Sphinx Theme</a> 0.16.1.
</p></div>
      
    </div>
  
</div>

  </footer>
  </body>
</html>