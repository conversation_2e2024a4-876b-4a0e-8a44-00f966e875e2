"""
This type stub file was generated by pyright.
"""

from inquirer.render.console.base import BaseConsoleRender

class Checkbox(BaseConsoleRender):
    def __init__(self, *args, **kwargs) -> None:
        ...
    
    def get_hint(self): # -> Literal['']:
        ...
    
    def default_choices(self): # -> list[Any]:
        ...
    
    @property
    def is_long(self): # -> bool:
        ...
    
    def get_options(self): # -> Generator[tuple[Any, Any | LiteralString, Any], Any, None]:
        ...
    
    def process_input(self, pressed): # -> None:
        ...
    
    def other_input(self): # -> None:
        ...
    


