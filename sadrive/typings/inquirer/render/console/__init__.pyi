"""
This type stub file was generated by pyright.
"""

import sys
from blessed import Terminal
from inquirer import errors, events, themes
from inquirer.render.console._checkbox import Checkbox
from inquirer.render.console._confirm import Confirm
from inquirer.render.console._editor import Editor
from inquirer.render.console._list import List
from inquirer.render.console._password import Password
from inquirer.render.console._path import Path
from inquirer.render.console._text import Text

class ConsoleRender:
    def __init__(self, event_generator=..., theme=..., *args, **kwargs) -> None:
        ...
    
    def render(self, question, answers=...): # -> Any:
        ...
    
    def render_error(self, message): # -> None:
        ...
    
    def render_in_bottombar(self, message): # -> None:
        ...
    
    def clear_bottombar(self): # -> None:
        ...
    
    def render_factory(self, question_type): # -> None:
        ...
    
    def print_line(self, base, lf=..., **kwargs): # -> None:
        ...
    
    def print_str(self, base, lf=..., **kwargs): # -> None:
        ...
    
    def clear_eos(self): # -> None:
        ...
    
    @property
    def width(self): # -> int:
        ...
    
    @property
    def height(self): # -> int:
        ...
    


