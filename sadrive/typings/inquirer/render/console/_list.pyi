"""
This type stub file was generated by pyright.
"""

from inquirer.render.console.base import BaseConsoleRender

class List(BaseConsoleRender):
    def __init__(self, *args, **kwargs) -> None:
        ...
    
    @property
    def is_long(self): # -> bool:
        ...
    
    def get_hint(self): # -> str:
        ...
    
    def get_options(self): # -> Generator[tuple[Any, Any | LiteralString | Literal['+', ' '], Any], Any, None]:
        ...
    
    def process_input(self, pressed): # -> None:
        ...
    


