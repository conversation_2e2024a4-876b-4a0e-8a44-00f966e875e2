"""
This type stub file was generated by pyright.
"""

"""Module that implements the questions types."""
class TaggedValue:
    def __init__(self, tag, value) -> None:
        ...
    
    def __str__(self) -> str:
        ...
    
    def __repr__(self): # -> str:
        ...
    
    def __eq__(self, other) -> bool:
        ...
    
    def __ne__(self, other) -> bool:
        ...
    
    def __hash__(self) -> int:
        ...
    


class Question:
    kind = ...
    def __init__(self, name, message=..., choices=..., default=..., ignore=..., validate=..., show_default=..., hints=..., other=...) -> None:
        ...
    
    def add_choice(self, choice): # -> int:
        ...
    
    @property
    def ignore(self): # -> bool:
        ...
    
    @property
    def message(self): # -> object | str:
        ...
    
    @property
    def default(self): # -> object | str:
        ...
    
    @property
    def choices_generator(self): # -> Generator[TaggedValue | Any | tuple[Any, ...], Any, None]:
        ...
    
    @property
    def choices(self): # -> list[TaggedValue | Any | tuple[Any, ...]]:
        ...
    
    def validate(self, current): # -> None:
        ...
    


class Text(Question):
    kind = ...
    def __init__(self, name, message=..., default=..., autocomplete=..., **kwargs) -> None:
        ...
    


class Password(Text):
    kind = ...
    def __init__(self, name, echo=..., **kwargs) -> None:
        ...
    


class Editor(Text):
    kind = ...


class Confirm(Question):
    kind = ...
    def __init__(self, name, default=..., **kwargs) -> None:
        ...
    


class List(Question):
    kind = ...
    def __init__(self, name, message=..., choices=..., hints=..., default=..., ignore=..., validate=..., carousel=..., other=..., autocomplete=...) -> None:
        ...
    


class Checkbox(Question):
    kind = ...
    def __init__(self, name, message=..., choices=..., hints=..., locked=..., default=..., ignore=..., validate=..., carousel=..., other=..., autocomplete=...) -> None:
        ...
    


class Path(Text):
    ANY = ...
    FILE = ...
    DIRECTORY = ...
    kind = ...
    def __init__(self, name, default=..., path_type=..., exists=..., **kwargs) -> None:
        ...
    
    def validate(self, current: str): # -> None:
        ...
    


def question_factory(kind, *args, **kwargs): # -> Editor | Checkbox | Confirm | List | Password | Path | Text:
    ...

def load_from_dict(question_dict) -> Question:
    """Load one question from a dict.

    It requires the keys 'name' and 'kind'.

    Returns:
        The Question object with associated data.
    """
    ...

def load_from_list(question_list) -> list[Question]:
    """Load a list of questions from a list of dicts.

    It requires the keys 'name' and 'kind' for each dict.

    Returns:
        A list of Question objects with associated data.
    """
    ...

def load_from_json(question_json) -> list | dict:
    """Load Questions from a JSON string.

    Returns:
        A list of Question objects with associated data if the JSON
        contains a list or a Question if the JSON contains a dict.
    """
    ...

