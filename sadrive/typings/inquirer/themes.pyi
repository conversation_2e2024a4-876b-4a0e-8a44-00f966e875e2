"""
This type stub file was generated by pyright.
"""

term = ...
def load_theme_from_json(json_theme): # -> Default:
    """Load a theme from a json.

    Expected format:
        >>> {
        ...     "Question": {
        ...         "mark_color": "yellow",
        ...         "brackets_color": "normal",
        ...         ...
        ...     },
        ...     "List": {
        ...         "selection_color": "bold_blue",
        ...         "selection_cursor": "->"
        ...     }
        ... }

    Color values should be string representing valid blessings.Terminal colors.
    """
    ...

def load_theme_from_dict(dict_theme): # -> Default:
    """Load a theme from a dict.

    Expected format:
        >>> {
        ...     "Question": {
        ...         "mark_color": "yellow",
        ...         "brackets_color": "normal",
        ...         ...
        ...     },
        ...     "List": {
        ...         "selection_color": "bold_blue",
        ...         "selection_cursor": "->"
        ...     }
        ... }

    Color values should be string representing valid blessings.Terminal colors.
    """
    ...

class Theme:
    def __init__(self) -> None:
        ...
    


class Default(Theme):
    def __init__(self) -> None:
        ...
    


class GreenPassion(Default):
    def __init__(self) -> None:
        ...
    


class BlueComposure(Default):
    def __init__(self) -> None:
        ...
    


