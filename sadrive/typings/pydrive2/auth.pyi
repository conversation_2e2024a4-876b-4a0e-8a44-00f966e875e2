"""
This type stub file was generated by pyright.
"""

from .apiattr import ApiAttributeMixin

class AuthError(Exception):
    """Base error for authentication/authorization errors."""
    ...


class InvalidCredentialsError(IOError):
    """Error trying to read credentials file."""
    ...


class AuthenticationRejected(AuthError):
    """User rejected authentication."""
    ...


class AuthenticationError(AuthError):
    """General authentication error."""
    ...


class RefreshError(AuthError):
    """Access token refresh error."""
    ...


def LoadAuth(decoratee): # -> _Wrapped[Callable[..., Any], Any, Callable[..., Any], Any]:
    """Decorator to check if the auth is valid and loads auth if not."""
    ...

def CheckServiceAuth(decoratee): # -> _Wrapped[Callable[..., Any], Any, Callable[..., Any], None]:
    """Decorator to authorize service account."""
    ...

def CheckAuth(decoratee): # -> _Wrapped[Callable[..., Any], Any, Callable[..., Any], None]:
    """Decorator to check if it requires OAuth2 flow request."""
    ...

class GoogleAuth(ApiAttributeMixin):
    """Wrapper class for oauth2client library in google-api-python-client.

    Loads all settings and credentials from one 'settings.yaml' file
    and performs common OAuth2.0 related functionality such as authentication
    and authorization.
    """
    DEFAULT_SETTINGS = ...
    CLIENT_CONFIGS_LIST = ...
    SERVICE_CONFIGS_LIST = ...
    settings = ...
    client_config = ...
    flow = ...
    credentials = ...
    http = ...
    service = ...
    auth_method = ...
    def __init__(self, settings_file=..., http_timeout=..., settings=...) -> None:
        """Create an instance of GoogleAuth.

        :param settings_file: path of settings file. 'settings.yaml' by default.
        :type settings_file: str.
        :param settings: settings dict.
        :type settings: dict.
        """
        ...
    
    @property
    def access_token_expired(self): # -> bool:
        """Checks if access token doesn't exist or is expired.

        :returns: bool -- True if access token doesn't exist or is expired.
        """
        ...
    
    @CheckAuth
    def LocalWebserverAuth(self, host_name=..., port_numbers=..., launch_browser=..., bind_addr=...):
        """Authenticate and authorize from user by creating local web server and
        retrieving authentication code.

        This function is not for web server application. It creates local web
        server for user from standalone application.

        If GDRIVE_NON_INTERACTIVE environment variable is set, this function
        raises AuthenticationError.

        :param host_name: host name of the local web server.
        :type host_name: str.
        :param port_numbers: list of port numbers to be tried to used.
        :type port_numbers: list.
        :param launch_browser: should browser be launched automatically
        :type launch_browser: bool
        :param bind_addr: optional IP address for the local web server to listen on.
            If not specified, it will listen on the address specified in the
            host_name parameter.
        :type bind_addr: str.
        :returns: str -- code returned from local web server
        :raises: AuthenticationRejected, AuthenticationError
        """
        ...
    
    @CheckAuth
    def CommandLineAuth(self): # -> str:
        """Authenticate and authorize from user by printing authentication url
        retrieving authentication code from command-line.

        :returns: str -- code returned from commandline.
        """
        ...
    
    @CheckServiceAuth
    def ServiceAuth(self): # -> None:
        """Authenticate and authorize using P12 private key, client id
        and client email for a Service account.
        :raises: AuthError, InvalidConfigError
        """
        ...
    
    def LoadCredentials(self, backend=...): # -> None:
        """Loads credentials or create empty credentials if it doesn't exist.

        :param backend: target backend to save credential to.
        :type backend: str.
        :raises: InvalidConfigError
        """
        ...
    
    def LoadCredentialsFile(self, credentials_file=...): # -> None:
        """Loads credentials or create empty credentials if it doesn't exist.

        Loads credentials file from path in settings if not specified.

        :param credentials_file: path of credentials file to read.
        :type credentials_file: str.
        :raises: InvalidConfigError, InvalidCredentialsError
        """
        ...
    
    def SaveCredentials(self, backend=...): # -> None:
        """Saves credentials according to specified backend.

        If you have any specific credentials backend in mind, don't use this
        function and use the corresponding function you want.

        :param backend: backend to save credentials.
        :type backend: str.
        :raises: InvalidConfigError
        """
        ...
    
    def SaveCredentialsFile(self, credentials_file=...): # -> None:
        """Saves credentials to the file in JSON format.

        :param credentials_file: destination to save file to.
        :type credentials_file: str.
        :raises: InvalidConfigError, InvalidCredentialsError
        """
        ...
    
    def LoadClientConfig(self, backend=...): # -> None:
        """Loads client configuration according to specified backend.

        If you have any specific backend to load client configuration from in mind,
        don't use this function and use the corresponding function you want.

        :param backend: backend to load client configuration from.
        :type backend: str.
        :raises: InvalidConfigError
        """
        ...
    
    def LoadClientConfigFile(self, client_config_file=...): # -> None:
        """Loads client configuration file downloaded from APIs console.

        Loads client config file from path in settings if not specified.

        :param client_config_file: path of client config file to read.
        :type client_config_file: str.
        :raises: InvalidConfigError
        """
        ...
    
    def LoadServiceConfigSettings(self): # -> None:
        """Loads client configuration from settings.
        :raises: InvalidConfigError
        """
        ...
    
    def LoadClientConfigSettings(self): # -> None:
        """Loads client configuration from settings file.

        :raises: InvalidConfigError
        """
        ...
    
    def GetFlow(self): # -> None:
        """Gets Flow object from client configuration.

        :raises: InvalidConfigError
        """
        ...
    
    def Refresh(self): # -> None:
        """Refreshes the access_token.

        :raises: RefreshError
        """
        ...
    
    def GetAuthUrl(self): # -> Literal[b""]:
        """Creates authentication url where user visits to grant access.

        :returns: str -- Authentication url.
        """
        ...
    
    def Auth(self, code): # -> None:
        """Authenticate, authorize, and build service.

        :param code: Code for authentication.
        :type code: str.
        :raises: AuthenticationError
        """
        ...
    
    def Authenticate(self, code): # -> None:
        """Authenticates given authentication code back from user.

        :param code: Code for authentication.
        :type code: str.
        :raises: AuthenticationError
        """
        ...
    
    def Authorize(self): # -> None:
        """Authorizes and builds service.

        :raises: AuthenticationError
        """
        ...
    
    def Get_Http_Object(self): # -> Http:
        """Create and authorize an httplib2.Http object. Necessary for
        thread-safety.
        :return: The http object to be used in each call.
        :rtype: httplib2.Http
        """
        ...
    


