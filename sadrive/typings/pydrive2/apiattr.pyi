"""
This type stub file was generated by pyright.
"""

class ApiAttribute:
    """A data descriptor that sets and returns values."""
    def __init__(self, name) -> None:
        """Create an instance of ApiAttribute.

        :param name: name of this attribute.
        :type name: str.
        """
        ...
    
    def __get__(self, obj, type=...):
        """Accesses value of this attribute."""
        ...
    
    def __set__(self, obj, value): # -> None:
        """Write value of this attribute."""
        ...
    
    def __del__(self, obj=...): # -> None:
        """Delete value of this attribute."""
        ...
    


class ApiAttributeMixin:
    """Mixin to initialize required global variables to use ApiAttribute."""
    def __init__(self) -> None:
        ...
    


class ApiResource(dict):
    """Super class of all api resources.

    Inherits and behaves as a python dictionary to handle api resources.
    Save clean copy of metadata in self.metadata as a dictionary.
    Provides changed metadata elements to efficiently update api resources.
    """
    auth = ...
    def __init__(self, *args, **kwargs) -> None:
        """Create an instance of ApiResource."""
        ...
    
    def __getitem__(self, key):
        """Overwritten method of dictionary.

        :param key: key of the query.
        :type key: str.
        :returns: value of the query.
        """
        ...
    
    def __setitem__(self, key, val): # -> None:
        """Overwritten method of dictionary.

        :param key: key of the query.
        :type key: str.
        :param val: value of the query.
        """
        ...
    
    def __repr__(self): # -> str:
        """Overwritten method of dictionary."""
        ...
    
    def update(self, *args, **kwargs): # -> None:
        """Overwritten method of dictionary."""
        ...
    
    def UpdateMetadata(self, metadata=...): # -> None:
        """Update metadata and mark all of them to be clean."""
        ...
    
    def GetChanges(self): # -> dict[Any, Any]:
        """Returns changed metadata elements to update api resources efficiently.

        :returns: dict -- changed metadata elements.
        """
        ...
    


class ApiResourceList(ApiAttributeMixin, ApiResource):
    """Abstract class of all api list resources.

    Inherits ApiResource and builds iterator to list any API resource.
    """
    metadata = ...
    def __init__(self, auth=..., metadata=...) -> None:
        """Create an instance of ApiResourceList.

        :param auth: authorized GoogleAuth instance.
        :type auth: GoogleAuth.
        :param metadata: parameter to send to list command.
        :type metadata: dict.
        """
        ...
    
    def __iter__(self): # -> Self:
        """Returns iterator object.

        :returns: ApiResourceList -- self
        """
        ...
    
    def __next__(self):
        """Make API call to list resources and return them.

        Auto updates 'pageToken' every time it makes API call and
        raises StopIteration when it reached the end of iteration.

        :returns: list -- list of API resources.
        :raises: StopIteration
        """
        ...
    
    def GetList(self): # -> list[Any]:
        """Get list of API resources.

        If 'maxResults' is not specified, it will automatically iterate through
        every resources available. Otherwise, it will make API call once and
        update 'pageToken'.

        :returns: list -- list of API resources.
        """
        ...
    
    def Reset(self): # -> None:
        """Resets current iteration"""
        ...
    


