"""
This type stub file was generated by pyright.
"""

SETTINGS_FILE = ...
SETTINGS_STRUCT = ...
class SettingsError(IOError):
    """Error while loading/saving settings"""
    ...


class InvalidConfigError(IOError):
    """Error trying to read client configuration."""
    ...


def LoadSettingsFile(filename=...): # -> Any:
    """Loads settings file in yaml format given file name.

    :param filename: path for settings file. 'settings.yaml' by default.
    :type filename: str.
    :raises: SettingsError
    """
    ...

def ValidateSettings(data): # -> None:
    """Validates if current settings is valid.

    :param data: dictionary containing all settings.
    :type data: dict.
    :raises: InvalidConfigError
    """
    ...

