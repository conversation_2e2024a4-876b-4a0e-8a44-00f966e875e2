"""
This type stub file was generated by pyright.
"""

from .apiattr import <PERSON>piAttributeMixin, ApiResource, ApiResourceList
from .auth import LoadAuth

BLOCK_SIZE = ...
MIME_TYPE_TO_BOM = ...
class FileNotUploadedError(RuntimeError):
    """Error trying to access metadata of file that is not uploaded."""
    ...


class ApiRequestError(IOError):
    def __init__(self, http_error) -> None:
        ...
    
    def GetField(self, field): # -> Any:
        """Returns the `field` from the first error"""
        ...
    


class FileNotDownloadableError(RuntimeError):
    """Error trying to download file that is not downloadable."""
    ...


def LoadMetadata(decoratee): # -> _Wrapped[Callable[..., Any], Any, Callable[..., Any], Any]:
    """Decorator to check if the file has metadata and fetches it if not.

    :raises: Api<PERSON>equestError, FileNotUploadedError
    """
    ...

class GoogleDriveFileList(ApiResourceList):
    """Google Drive FileList instance.

    Equivalent to Files.list() in Drive APIs.
    """
    def __init__(self, auth=..., param=...) -> None:
        """Create an instance of GoogleDriveFileList."""
        ...
    


class IoBuffer:
    """Lightweight retention of one chunk."""
    def __init__(self, encoding) -> None:
        ...
    
    def write(self, chunk): # -> None:
        ...
    
    def read(self): # -> None:
        ...
    


class MediaIoReadable:
    def __init__(self, request, encoding=..., pre_buffer=..., remove_prefix=..., chunksize=...) -> None:
        """File-like wrapper around MediaIoBaseDownload.

        :param pre_buffer: Whether to read one chunk into an internal buffer
        immediately in order to raise any potential errors.
        :param remove_prefix: Bytes prefix to remove from internal pre_buffer.
        :raises: ApiRequestError
        """
        ...
    
    def read(self): # -> None:
        """
        :returns: bytes or str -- chunk (or None if done)
        :raises: ApiRequestError
        """
        ...
    
    def __iter__(self): # -> Generator[Any, Any, None]:
        """
        :raises: ApiRequestError
        """
        ...
    
    def __len__(self): # -> None:
        ...
    


class GoogleDriveFile(ApiAttributeMixin, ApiResource):
    """Google Drive File instance.

    Inherits ApiResource which inherits dict.
    Can access and modify metadata like dictionary.
    """
    content = ...
    uploaded = ...
    metadata = ...
    def __init__(self, auth=..., metadata=..., uploaded=...) -> None:
        """Create an instance of GoogleDriveFile.

        :param auth: authorized GoogleAuth instance.
        :type auth: pydrive2.auth.GoogleAuth
        :param metadata: file resource to initialize GoogleDriveFile with.
        :type metadata: dict.
        :param uploaded: True if this file is confirmed to be uploaded.
        :type uploaded: bool.
        """
        ...
    
    def __getitem__(self, key):
        """Overwrites manner of accessing Files resource.

        If this file instance is not uploaded and id is specified,
        it will try to look for metadata with Files.get().

        :param key: key of dictionary query.
        :type key: str.
        :returns: value of Files resource
        :raises: KeyError, FileNotUploadedError
        """
        ...
    
    def SetContentString(self, content, encoding=...): # -> None:
        """Set content of this file to be a string.

        Creates io.BytesIO instance of utf-8 encoded string.
        Sets mimeType to be 'text/plain' if not specified and file id is not
        set (means that we are uploading this file for the first time).

        :param encoding: The encoding to use when setting the content of this file.
        :type encoding: str
        :param content: content of the file in string.
        :type content: str
        """
        ...
    
    def SetContentFile(self, filename): # -> None:
        """Set content of this file from a file.

        Opens the file specified by this method.
        Will be read, uploaded, and closed by Upload() method.
        Sets metadata 'title' and 'mimeType' automatically if not specified and
        the file is uploaded for the first time (id is not set).

        :param filename: name of the file to be uploaded.
        :type filename: str.
        """
        ...
    
    def GetContentString(self, mimetype=..., encoding=..., remove_bom=...): # -> str:
        """Get content of this file as a string.

        :param mimetype: The mimetype of the content string.
        :type mimetype: str

        :param encoding: The encoding to use when decoding the byte string.
        :type encoding: str

        :param remove_bom: Whether to strip a known BOM.
        :type remove_bom: bool

        :returns: str -- utf-8 decoded content of the file
        :raises: ApiRequestError, FileNotUploadedError, FileNotDownloadableError
        """
        ...
    
    @LoadAuth
    def GetContentFile(self, filename, mimetype=..., remove_bom=..., callback=..., chunksize=..., acknowledge_abuse=...): # -> None:
        """Save content of this file as a local file.

        :param filename: name of the file to write to.
        :type filename: str
        :param mimetype: mimeType of the file.
        :type mimetype: str
        :param remove_bom: Whether to remove the byte order marking.
        :type remove_bom: bool
        :param callback: passed two arguments: (total transferred, file size).
        :type param: callable
        :param chunksize: chunksize in bytes (standard 100 MB(1024*1024*100))
        :type chunksize: int
        :param acknowledge_abuse: Acknowledging the risk and download file
            identified as abusive.
        :type acknowledge_abuse: bool
        :raises: ApiRequestError, FileNotUploadedError
        """
        ...
    
    @LoadAuth
    def GetContentIOBuffer(self, mimetype=..., encoding=..., remove_bom=..., chunksize=..., acknowledge_abuse=...): # -> MediaIoReadable:
        """Get a file-like object which has a buffered read() method.

        :param mimetype: mimeType of the file.
        :type mimetype: str
        :param encoding: The encoding to use when decoding the byte string.
        :type encoding: str
        :param remove_bom: Whether to remove the byte order marking.
        :type remove_bom: bool
        :param chunksize: default read()/iter() chunksize.
        :type chunksize: int
        :param acknowledge_abuse: Acknowledging the risk and download file
            identified as abusive.
        :type acknowledge_abuse: bool
        :returns: MediaIoReadable -- file-like object.
        :raises: ApiRequestError, FileNotUploadedError
        """
        ...
    
    @LoadAuth
    def FetchMetadata(self, fields=..., fetch_all=...): # -> None:
        """Download file's metadata from id using Files.get().

        :param fields: The fields to include, as one string, each entry separated
                       by commas, e.g. 'fields,labels'.
        :type fields: str

        :param fetch_all: Whether to fetch all fields.
        :type fetch_all: bool

        :raises: ApiRequestError, FileNotUploadedError
        """
        ...
    
    @LoadMetadata
    def FetchContent(self, mimetype=..., remove_bom=...): # -> None:
        """Download file's content from download_url.

        :raises: ApiRequestError, FileNotUploadedError, FileNotDownloadableError
        """
        ...
    
    def Upload(self, param=...): # -> None:
        """Upload/update file by choosing the most efficient method.

        :param param: additional parameter to upload file.
        :type param: dict.
        :raises: ApiRequestError
        """
        ...
    
    def Trash(self, param=...): # -> None:
        """Move a file to the trash.

        :raises: ApiRequestError
        """
        ...
    
    def UnTrash(self, param=...): # -> None:
        """Move a file out of the trash.
        :param param: Additional parameter to file.
        :type param: dict.
        :raises: ApiRequestError
        """
        ...
    
    def Delete(self, param=...): # -> None:
        """Hard-delete a file.

        :param param: additional parameter to file.
        :type param: dict.
        :raises: ApiRequestError
        """
        ...
    
    @LoadAuth
    def Copy(self, target_folder=..., new_title=..., param=...): # -> GoogleDriveFile:
        """Creates a copy of this file. Folders cannot be copied.

        :param target_folder: Folder where the file will be copied.
        :type target_folder: GoogleDriveFile, optional
        :param new_title: Name of the new file.
        :type new_title: str, optional
        :param param: addition parameters to pass.
        :type param: dict, optional
        :raises: ApiRequestError
        :return: the copied file
        :rtype: GoogleDriveFile
        """
        ...
    
    def InsertPermission(self, new_permission, param=...):
        """Insert a new permission. Re-fetches all permissions after call.

        :param new_permission: The new permission to insert, please see the
                               official Google Drive API guide on permissions.insert
                               for details.
        :type new_permission: object

        :param param: addition parameters to pass
        :type param: dict

        :return: The permission object.
        :rtype: object
        """
        ...
    
    @LoadAuth
    def GetPermissions(self):
        """Get file's or shared drive's permissions.

        For files in a shared drive, at most 100 results will be returned.
        It doesn't paginate and collect all results.

        :return: A list of the permission objects.
        :rtype: object[]
        """
        ...
    
    def DeletePermission(self, permission_id): # -> Literal[True]:
        """Deletes the permission specified by the permission_id.

        :param permission_id: The permission id.
        :type permission_id: str
        :return: True if it succeeds.
        :rtype: bool
        """
        ...
    
    @LoadAuth
    def GetRevisions(self):
        """Get file's or shared drive's revisions.

        For files in a shared drive, at most 100 results will be returned.
        It doesn't paginate and collect all results.

        :return: A list of the revision objects.
        :rtype: object[]
        """
        ...
    


