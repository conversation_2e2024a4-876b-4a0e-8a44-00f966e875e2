#!/usr/bin/env python3
"""
Tạo infrastructure hoàn chỉnh 17TiB SA Drive
12 projects × 100 service accounts = 1200 SAs = 18TB
"""

import subprocess
import time
import json
import threading
import queue
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

class SA17TBCreator:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.accounts_dir = self.base_dir / "config_dir" / "accounts"
        self.accounts_dir.mkdir(parents=True, exist_ok=True)
        
        # Xóa service accounts cũ
        for old_file in self.accounts_dir.glob("*.json"):
            old_file.unlink()
        
        self.projects = []
        self.total_sas = 0
        self.target_projects = 12
        self.sas_per_project = 100
        
        print("🚀 Tạo Infrastructure 17TiB SA Drive")
        print("=" * 50)
        print(f"🎯 Mục tiêu: {self.target_projects} projects × {self.sas_per_project} SAs = {self.target_projects * self.sas_per_project} SAs")
        print(f"💾 Storage: {self.target_projects * self.sas_per_project * 15}GB = {self.target_projects * self.sas_per_project * 15 / 1024:.1f}TiB")
    
    def create_project_batch(self, start_idx, end_idx):
        """Tạo batch projects song song"""
        projects_created = []
        
        for i in range(start_idx, end_idx + 1):
            project_id = f"sa-drive-{i:02d}-{int(time.time())}-{i:02d}"
            
            try:
                print(f"🏗️  [{i:02d}/{self.target_projects}] Tạo project: {project_id}")
                
                # Tạo project
                subprocess.run([
                    'gcloud', 'projects', 'create', project_id,
                    '--name', f'SA Drive Project {i}',
                    '--quiet'
                ], check=True, capture_output=True, timeout=60)
                
                # Set project
                subprocess.run(['gcloud', 'config', 'set', 'project', project_id], 
                             check=True, capture_output=True, timeout=30)
                
                # Enable APIs
                apis = ['drive.googleapis.com', 'iam.googleapis.com']
                for api in apis:
                    subprocess.run([
                        'gcloud', 'services', 'enable', api, '--quiet'
                    ], check=True, capture_output=True, timeout=60)
                
                projects_created.append(project_id)
                print(f"✅ [{i:02d}] Project ready: {project_id}")
                
                # Nghỉ giữa projects để tránh rate limit
                time.sleep(5)
                
            except subprocess.CalledProcessError as e:
                print(f"❌ [{i:02d}] Lỗi tạo project {project_id}: {e}")
            except subprocess.TimeoutExpired:
                print(f"⏰ [{i:02d}] Timeout tạo project {project_id}")
        
        return projects_created
    
    def create_service_accounts_batch(self, project_id, project_idx, batch_size=10):
        """Tạo service accounts theo batch"""
        print(f"👥 [{project_idx:02d}] Tạo {self.sas_per_project} SAs cho {project_id}")
        
        # Set project
        subprocess.run(['gcloud', 'config', 'set', 'project', project_id], 
                     check=True, capture_output=True)
        
        success_count = 0
        
        # Tạo theo batch
        for batch_start in range(0, self.sas_per_project, batch_size):
            batch_end = min(batch_start + batch_size, self.sas_per_project)
            
            print(f"  📦 [{project_idx:02d}] Batch {batch_start}-{batch_end-1}")
            
            for i in range(batch_start, batch_end):
                sa_name = f"sa{i:03d}"
                sa_email = f"{sa_name}@{project_id}.iam.gserviceaccount.com"
                key_file = self.accounts_dir / f"{project_idx:02d}-{i:03d}.json"
                
                try:
                    # Tạo service account
                    subprocess.run([
                        'gcloud', 'iam', 'service-accounts', 'create', sa_name,
                        '--display-name', f'SA Drive {project_idx}-{i}',
                        '--quiet'
                    ], check=True, capture_output=True, timeout=30)
                    
                    # Tạo key
                    subprocess.run([
                        'gcloud', 'iam', 'service-accounts', 'keys', 'create',
                        str(key_file),
                        '--iam-account', sa_email,
                        '--quiet'
                    ], check=True, capture_output=True, timeout=30)
                    
                    success_count += 1
                    
                    # Rate limiting
                    time.sleep(1)
                    
                except subprocess.CalledProcessError:
                    print(f"    ❌ Lỗi tạo SA {sa_name}")
                except subprocess.TimeoutExpired:
                    print(f"    ⏰ Timeout tạo SA {sa_name}")
            
            # Nghỉ giữa các batch
            time.sleep(3)
            
            if (batch_end) % 20 == 0:
                print(f"  ✅ [{project_idx:02d}] Đã tạo {batch_end}/{self.sas_per_project} SAs")
        
        print(f"🎉 [{project_idx:02d}] Hoàn thành: {success_count}/{self.sas_per_project} SAs")
        return success_count
    
    def create_infrastructure_fast(self):
        """Tạo infrastructure nhanh với parallel processing"""
        print("\n🚀 Bắt đầu tạo 17TiB Infrastructure...")
        print("⚠️  Quá trình này sẽ mất 1-2 giờ")
        
        confirm = input("Tiếp tục? (y/N): ")
        if confirm.lower() != 'y':
            return
        
        start_time = time.time()
        
        # Phase 1: Tạo projects (song song)
        print("\n📋 Phase 1: Tạo 12 projects...")
        all_projects = []
        
        # Tạo projects theo batch nhỏ để tránh rate limit
        for batch_start in range(1, self.target_projects + 1, 3):
            batch_end = min(batch_start + 2, self.target_projects)
            batch_projects = self.create_project_batch(batch_start, batch_end)
            all_projects.extend(batch_projects)
            
            # Nghỉ giữa các batch projects
            if batch_end < self.target_projects:
                print("⏳ Nghỉ 30s giữa batch projects...")
                time.sleep(30)
        
        print(f"\n✅ Phase 1 hoàn thành: {len(all_projects)}/{self.target_projects} projects")
        
        # Phase 2: Tạo service accounts
        print("\n👥 Phase 2: Tạo service accounts...")
        total_sas = 0
        
        for idx, project_id in enumerate(all_projects, 1):
            try:
                sa_count = self.create_service_accounts_batch(project_id, idx)
                total_sas += sa_count
                
                # Nghỉ giữa projects
                if idx < len(all_projects):
                    print(f"⏳ Nghỉ 60s trước project tiếp theo...")
                    time.sleep(60)
                    
            except Exception as e:
                print(f"❌ Lỗi tạo SAs cho project {idx}: {e}")
        
        # Tính toán kết quả
        elapsed_time = time.time() - start_time
        storage_gb = total_sas * 15
        storage_tb = storage_gb / 1024
        
        print(f"\n🎉 17TiB Infrastructure hoàn thành!")
        print("=" * 50)
        print(f"📊 Kết quả:")
        print(f"  - Projects: {len(all_projects)}")
        print(f"  - Service Accounts: {total_sas}")
        print(f"  - Storage: {storage_gb}GB = {storage_tb:.1f}TiB")
        print(f"  - Thời gian: {elapsed_time/3600:.1f} giờ")
        
        # Tạo summary
        self.create_infrastructure_summary(all_projects, total_sas)
        
        return all_projects, total_sas
    
    def create_infrastructure_summary(self, projects, total_sas):
        """Tạo summary infrastructure"""
        summary = {
            "created_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "projects": projects,
            "total_service_accounts": total_sas,
            "storage_gb": total_sas * 15,
            "storage_tb": (total_sas * 15) / 1024,
            "accounts_dir": str(self.accounts_dir.absolute())
        }
        
        with open("infrastructure_summary.json", 'w') as f:
            json.dump(summary, f, indent=2)
        
        # Tạo README
        readme = f"""# 🎉 17TiB SA Drive Infrastructure

## 📊 Infrastructure Summary
- **Created**: {summary['created_at']}
- **Projects**: {len(projects)}
- **Service Accounts**: {total_sas}
- **Total Storage**: {summary['storage_gb']}GB = {summary['storage_tb']:.1f}TiB

## 🚀 Usage

### Setup gclone config
```bash
./gclone/gclone config create mydrive drive \\
    service_account_file config_dir/accounts/01-001.json \\
    service_account_file_path config_dir/accounts/
```

### Upload files
```bash
./gclone/gclone copy myfile.txt mydrive:
./gclone/gclone copy myfolder/ mydrive:backup/
```

### Mount as filesystem
```bash
mkdir -p ~/sa-drive-mount
./gclone/gclone mount mydrive: ~/sa-drive-mount
```

### Sync folders
```bash
./gclone/gclone sync ./local-folder mydrive:remote-folder/
```

## 📁 Projects Created
"""
        
        for i, project in enumerate(projects, 1):
            readme += f"- Project {i:02d}: `{project}`\n"
        
        readme += f"""
## 🔧 Service Accounts
- Location: `{self.accounts_dir}`
- Format: `XX-YYY.json` (Project-ServiceAccount)
- Total: {total_sas} files

## 🎯 Best Case Achieved!
You now have {summary['storage_tb']:.1f}TiB of Google Drive storage!
"""
        
        with open("17TB_INFRASTRUCTURE.md", 'w') as f:
            f.write(readme)
        
        print(f"📝 Đã tạo infrastructure_summary.json")
        print(f"📖 Đã tạo 17TB_INFRASTRUCTURE.md")

def main():
    creator = SA17TBCreator()
    
    print("🎯 17TiB SA Drive Infrastructure Creator")
    print("=" * 45)
    print("Tùy chọn:")
    print("1. Tạo Full Infrastructure (12 projects × 100 SAs = 17TiB)")
    print("2. Tạo Test Infrastructure (3 projects × 20 SAs = 900GB)")
    print("3. Thoát")
    
    choice = input("Nhập lựa chọn (1-3): ").strip()
    
    if choice == "1":
        creator.create_infrastructure_fast()
    elif choice == "2":
        creator.target_projects = 3
        creator.sas_per_project = 20
        creator.create_infrastructure_fast()
    elif choice == "3":
        print("👋 Tạm biệt!")
    else:
        print("❌ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
