#!/usr/bin/env python3
"""
Realistic approach to achieve 17TB Google Drive storage
"""

import subprocess
import time
import json
from pathlib import Path

def check_current_infrastructure():
    """Kiểm tra infrastructure hiện có"""
    print("📊 Kiểm tra infrastructure hiện tại...")
    
    # Kiểm tra projects
    try:
        result = subprocess.run([
            'gcloud', 'projects', 'list', '--filter=name:sa-drive*', '--format=value(projectId)'
        ], capture_output=True, text=True, timeout=30)
        
        projects = [p.strip() for p in result.stdout.strip().split('\n') if p.strip()]
        print(f"✅ Tìm thấy {len(projects)} projects:")
        for project in projects:
            print(f"  - {project}")
    except:
        projects = []
        print("❌ Không thể kiểm tra projects")
    
    # Kiểm tra service accounts
    accounts_dir = Path("config_dir/accounts")
    json_files = list(accounts_dir.glob("*.json"))
    print(f"✅ Tìm thấy {len(json_files)} service accounts")
    print(f"💾 Storage hiện tại: {len(json_files) * 15}GB = {(len(json_files) * 15)/1024:.1f}TiB")
    
    return projects, json_files

def create_optimized_approach():
    """Tạo approach tối ưu để đạt 17TB"""
    print("\n🎯 Approach tối ưu để đạt 17TB:")
    print("=" * 40)
    
    approaches = [
        {
            "name": "Current Infrastructure + Scale",
            "description": "Sử dụng 8 projects hiện có + tạo thêm SAs",
            "storage": "8 projects × 100 SAs = 12TB",
            "feasibility": "High",
            "time": "2-3 hours"
        },
        {
            "name": "Multiple Gmail Accounts",
            "description": "Sử dụng nhiều Gmail accounts",
            "storage": "2 Gmail × 8 projects = 24TB",
            "feasibility": "High", 
            "time": "1 day"
        },
        {
            "name": "Existing + Purchased Storage",
            "description": "Kết hợp SA Drive + Google One",
            "storage": "Current + 2TB Google One = 14TB+",
            "feasibility": "Immediate",
            "time": "5 minutes"
        }
    ]
    
    for i, approach in enumerate(approaches, 1):
        print(f"\n{i}. {approach['name']}")
        print(f"   📋 {approach['description']}")
        print(f"   💾 Storage: {approach['storage']}")
        print(f"   ✅ Feasibility: {approach['feasibility']}")
        print(f"   ⏰ Time: {approach['time']}")

def maximize_current_infrastructure():
    """Tối đa hóa infrastructure hiện tại"""
    print("\n🚀 Tối đa hóa infrastructure hiện tại...")
    
    projects, json_files = check_current_infrastructure()
    
    if len(projects) >= 8:
        print(f"\n✅ Có {len(projects)} projects - có thể đạt {len(projects) * 1.5:.1f}TB")
        
        # Tạo script để thêm SAs vào projects hiện có
        script_content = f"""#!/bin/bash
# Script để thêm service accounts vào projects hiện có

echo "🚀 Thêm service accounts để đạt 17TB..."

# Projects hiện có
PROJECTS=("""
        
        for project in projects:
            script_content += f'"{project}" '
        
        script_content += """)

# Thêm service accounts cho mỗi project
for PROJECT in "${PROJECTS[@]}"; do
    echo "👥 Thêm SAs cho project: $PROJECT"
    gcloud config set project $PROJECT
    
    # Tạo thêm 50 service accounts (tổng 100 per project)
    for i in {50..99}; do
        SA_NAME="sa$(printf "%03d" $i)"
        SA_EMAIL="${SA_NAME}@${PROJECT}.iam.gserviceaccount.com"
        
        echo "  Tạo SA: $SA_NAME"
        
        # Tạo service account
        gcloud iam service-accounts create $SA_NAME \\
            --display-name "SA Drive $i" \\
            --quiet 2>/dev/null
        
        # Tạo key
        gcloud iam service-accounts keys create \\
            "config_dir/accounts/${PROJECT}-${i}.json" \\
            --iam-account $SA_EMAIL \\
            --quiet 2>/dev/null
        
        sleep 2
    done
    
    echo "✅ Hoàn thành project: $PROJECT"
    sleep 30
done

echo "🎉 Hoàn thành! Kiểm tra storage:"
python3 17tb_final_summary.py
"""
        
        with open("maximize_current.sh", 'w') as f:
            f.write(script_content)
        
        import os
        os.chmod("maximize_current.sh", 0o755)
        
        print("✅ Đã tạo script: maximize_current.sh")
        print("🚀 Chạy: ./maximize_current.sh để thêm SAs")

def create_multi_gmail_guide():
    """Tạo hướng dẫn sử dụng nhiều Gmail"""
    guide = """# 🚀 Multi-Gmail Strategy for 17TB+

## Concept
Mỗi Gmail account có thể tạo 12 projects × 100 SAs = 18TB
Với 2 Gmail accounts = 36TB storage!

## Setup Process

### Gmail Account 1 (Current)
- ✅ 8 projects created
- 🔄 Scale to 100 SAs per project = 12TB

### Gmail Account 2 (New)
1. Tạo Gmail account mới
2. Enable Google Cloud
3. Chạy script tạo 8-12 projects
4. Tạo 100 SAs per project = 12-18TB

### Gmail Account 3+ (Optional)
- Repeat process for unlimited storage

## Implementation

### Step 1: Maximize Current Account
```bash
./maximize_current.sh
```

### Step 2: Setup New Gmail Account
```bash
# Login with new Gmail
gcloud auth login --account <EMAIL>

# Create projects for new account
python3 create_17tb_infrastructure.py
```

### Step 3: Combine Storage
```bash
# Configure gclone with multiple accounts
./gclone/gclone config create gmail1 drive service_account_file_path config_dir/gmail1/
./gclone/gclone config create gmail2 drive service_account_file_path config_dir/gmail2/

# Use both storages
./gclone/gclone copy bigfile.zip gmail1:
./gclone/gclone copy bigfile2.zip gmail2:
```

## Result
- Gmail 1: 12TB
- Gmail 2: 18TB  
- **Total: 30TB+ storage**

## Management
```bash
# Check storage across accounts
./gclone/gclone about gmail1:
./gclone/gclone about gmail2:

# Distribute files
./gclone/gclone copy folder1/ gmail1:backup/
./gclone/gclone copy folder2/ gmail2:backup/
```

**Unlimited scaling with additional Gmail accounts!**
"""
    
    with open("MULTI_GMAIL_STRATEGY.md", 'w') as f:
        f.write(guide)
    
    print("📖 Đã tạo MULTI_GMAIL_STRATEGY.md")

def create_immediate_solution():
    """Tạo giải pháp ngay lập tức"""
    print("\n⚡ Giải pháp ngay lập tức để có 17TB:")
    print("=" * 45)
    
    projects, json_files = check_current_infrastructure()
    current_storage = len(json_files) * 15
    
    print(f"📊 Storage hiện tại: {current_storage}GB = {current_storage/1024:.1f}TB")
    
    # Option 1: Google One
    print(f"\n1️⃣  Google One (Trả phí - Ngay lập tức)")
    print(f"   💰 2TB: $9.99/month")
    print(f"   💰 10TB: $99.99/month") 
    print(f"   💰 20TB: $199.99/month")
    print(f"   ✅ Kết hợp với SA Drive = {current_storage/1024:.1f}TB + 20TB = {current_storage/1024 + 20:.1f}TB")
    
    # Option 2: Maximize current
    potential_storage = len(projects) * 100 * 15
    print(f"\n2️⃣  Maximize Current Infrastructure (Miễn phí)")
    print(f"   🔄 {len(projects)} projects × 100 SAs = {potential_storage}GB = {potential_storage/1024:.1f}TB")
    print(f"   ⏰ Thời gian: 2-3 giờ")
    print(f"   🚀 Chạy: ./maximize_current.sh")
    
    # Option 3: Multi-Gmail
    print(f"\n3️⃣  Multi-Gmail Strategy (Miễn phí)")
    print(f"   📧 2 Gmail accounts = 2 × 18TB = 36TB")
    print(f"   ⏰ Thời gian: 1 ngày")
    print(f"   📖 Hướng dẫn: MULTI_GMAIL_STRATEGY.md")

def main():
    print("🎯 Achieve 17TB Google Drive Storage")
    print("=" * 40)
    
    # Kiểm tra infrastructure hiện tại
    projects, json_files = check_current_infrastructure()
    
    # Tạo approaches
    create_optimized_approach()
    
    # Tối đa hóa hiện tại
    if len(projects) > 0:
        maximize_current_infrastructure()
    
    # Multi-Gmail strategy
    create_multi_gmail_guide()
    
    # Immediate solution
    create_immediate_solution()
    
    print(f"\n🎉 Summary:")
    print(f"📊 Current: {len(json_files) * 15}GB = {(len(json_files) * 15)/1024:.1f}TB")
    print(f"🎯 Target: 17TB")
    print(f"🚀 Best approach: Multi-Gmail strategy")
    print(f"⚡ Immediate: Maximize current + Google One")
    
    print(f"\n📋 Next Steps:")
    print(f"1. Chạy: ./maximize_current.sh (nếu có)")
    print(f"2. Đọc: MULTI_GMAIL_STRATEGY.md")
    print(f"3. Hoặc: Mua Google One 20TB")

if __name__ == "__main__":
    main()
