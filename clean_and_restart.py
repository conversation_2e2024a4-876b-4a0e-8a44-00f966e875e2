#!/usr/bin/env python3
"""
Clean và restart SA Drive setup
"""

import subprocess
import time
import json
from pathlib import Path

def clean_project(project_id):
    """Xóa tất cả service accounts trong project"""
    print(f"🧹 Cleaning project: {project_id}")
    
    try:
        # Set project
        subprocess.run(['gcloud', 'config', 'set', 'project', project_id], 
                     check=True, capture_output=True)
        
        # L<PERSON>y danh sách service accounts
        result = subprocess.run([
            'gcloud', 'iam', 'service-accounts', 'list', 
            '--project', project_id, '--format=value(email)'
        ], capture_output=True, text=True, check=True)
        
        sa_emails = [email.strip() for email in result.stdout.strip().split('\n') if email.strip()]
        
        print(f"📋 Tìm thấy {len(sa_emails)} service accounts")
        
        # Xóa từng service account
        for sa_email in sa_emails:
            if 'sa-' in sa_email:  # Chỉ xóa SA của chúng ta
                try:
                    subprocess.run([
                        'gcloud', 'iam', 'service-accounts', 'delete', sa_email,
                        '--project', project_id, '--quiet'
                    ], check=True, capture_output=True)
                    print(f"  ✅ Đã xóa: {sa_email}")
                except subprocess.CalledProcessError:
                    print(f"  ⚠️  Không thể xóa: {sa_email}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi cleaning project: {e}")
        return False

def delete_project(project_id):
    """Xóa hoàn toàn project"""
    print(f"🗑️  Xóa project: {project_id}")
    
    try:
        subprocess.run([
            'gcloud', 'projects', 'delete', project_id, '--quiet'
        ], check=True, capture_output=True)
        print(f"✅ Đã xóa project: {project_id}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi xóa project: {e}")
        return False

def create_simple_test():
    """Tạo test đơn giản với 1 project, 5 SAs"""
    print("🧪 Tạo Simple Test: 1 project × 5 SAs = 75GB")
    
    # Xóa service accounts cũ
    accounts_dir = Path("config_dir/accounts")
    for old_file in accounts_dir.glob("*.json"):
        old_file.unlink()
        print(f"🗑️  Xóa file cũ: {old_file.name}")
    
    # Tạo project mới
    project_id = f"sa-drive-simple-{int(time.time())}"
    
    try:
        print(f"🏗️  Tạo project: {project_id}")
        
        # Tạo project
        subprocess.run([
            'gcloud', 'projects', 'create', project_id,
            '--name', 'SA Drive Simple Test'
        ], check=True, capture_output=True)
        
        # Set project
        subprocess.run(['gcloud', 'config', 'set', 'project', project_id], 
                     check=True, capture_output=True)
        
        print("⏳ Đợi project được khởi tạo...")
        time.sleep(10)
        
        # Enable APIs
        print("🔧 Enable APIs...")
        subprocess.run([
            'gcloud', 'services', 'enable', 
            'drive.googleapis.com',
            'iam.googleapis.com',
            'cloudresourcemanager.googleapis.com'
        ], check=True, capture_output=True)
        
        print("⏳ Đợi APIs được enable...")
        time.sleep(20)
        
        # Tạo 5 service accounts
        success_count = 0
        for i in range(5):
            sa_name = f"sadrive{i:02d}"  # Tên khác để tránh conflict
            sa_email = f"{sa_name}@{project_id}.iam.gserviceaccount.com"
            key_file = accounts_dir / f"{i}.json"
            
            try:
                print(f"👤 Tạo service account {i+1}/5: {sa_name}")
                
                # Tạo service account
                subprocess.run([
                    'gcloud', 'iam', 'service-accounts', 'create', sa_name,
                    '--display-name', f'SA Drive {i}',
                    '--description', f'Service Account for SA Drive {i}',
                    '--project', project_id
                ], check=True, capture_output=True, text=True)
                
                print(f"  ✅ Đã tạo SA: {sa_name}")
                
                # Đợi SA được tạo hoàn toàn
                time.sleep(5)
                
                # Tạo key
                subprocess.run([
                    'gcloud', 'iam', 'service-accounts', 'keys', 'create',
                    str(key_file),
                    '--iam-account', sa_email,
                    '--project', project_id
                ], check=True, capture_output=True, text=True)
                
                print(f"  ✅ Đã tạo key: {key_file.name}")
                success_count += 1
                
                # Nghỉ giữa các SAs
                time.sleep(3)
                
            except subprocess.CalledProcessError as e:
                print(f"  ❌ Lỗi tạo SA {sa_name}: {e}")
                continue
        
        print(f"\n🎉 Simple Test hoàn thành!")
        print(f"📊 Đã tạo: {success_count}/5 service accounts")
        print(f"💾 Storage: {success_count * 15}GB")
        print(f"📁 Project: {project_id}")
        
        # Test SA Drive
        print("\n🧪 Test SA Drive...")
        try:
            result = subprocess.run(['sadrive', 'details'], 
                                  capture_output=True, text=True, timeout=30)
            print("📊 SA Drive Details:")
            print(result.stdout)
            if result.stderr:
                print("Errors:", result.stderr)
        except subprocess.TimeoutExpired:
            print("⏰ SA Drive timeout - có thể cần thời gian để sync")
        except Exception as e:
            print(f"❌ Lỗi test SA Drive: {e}")
        
        return project_id, success_count
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Lỗi tạo simple test: {e}")
        return None, 0

def main():
    print("🧹 Clean và Restart SA Drive Setup")
    print("=" * 40)
    
    print("Chọn action:")
    print("1. Clean project hiện tại và tạo Simple Test (5 SAs)")
    print("2. Xóa hoàn toàn project cũ")
    print("3. Tạo Simple Test mới (không xóa gì)")
    
    choice = input("Nhập lựa chọn (1-3): ").strip()
    
    if choice == "1":
        # Clean project cũ
        old_project = "sa-drive-01-443518-jjet"
        clean_project(old_project)
        
        # Tạo simple test
        create_simple_test()
        
    elif choice == "2":
        old_project = "sa-drive-01-443518-jjet"
        delete_project(old_project)
        
    elif choice == "3":
        create_simple_test()
        
    else:
        print("❌ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
