# 🚀 SA Drive Setup Guide - 17TiB Storage

## Mục tiêu: Tạo 17TiB Google Drive Storage miễn phí

**Best Case Scenario**: 12 projects × 100 service accounts = 1200 SAs = 18,000GB ≈ **17TiB**

## ✅ Đã hoàn thành:
- [x] Cài đặt SA Drive CLI
- [x] Cài đặt và cấu hình gclone
- [x] Cài đặt Google Cloud CLI
- [x] Thiết lập cấu trúc thư mục

## 📋 Các bước tiếp theo:

### Bước 1: Đ<PERSON><PERSON> nhập Google Cloud
```bash
# Đăng nhập với Gmail account của bạn
gcloud auth login

# Kiểm tra đăng nhập
gcloud auth list
```

### Bước 2: Tạo Service Accounts

#### Option A: Test Setup (Khuyến nghị để bắt đầu)
```bash
# Tạo 2 projects × 10 SAs = 300GB để test
python3 create_real_service_accounts.py
# Chọn option 1
```

#### Option B: Full Setup (17TiB)
```bash
# Tạo 12 projects × 100 SAs = 17TiB (mất 2-3 giờ)
python3 create_real_service_accounts.py
# Chọn option 2
```

### Bước 3: Thiết lập Google Drive Folder

1. Truy cập [Google Drive](https://drive.google.com)
2. Tạo folder mới tên "SA-Drive"
3. Click chuột phải → "Get link" → Copy link
4. Lấy ID từ link (phần sau `/folders/`)
5. Cập nhật `parent_id` trong `config_dir/config.json`

### Bước 4: Chia sẻ quyền truy cập

Chạy script để chia sẻ folder với tất cả service accounts:
```bash
python3 share_with_service_accounts.py
```

### Bước 5: Test SA Drive

```bash
# Kiểm tra service accounts
sadrive details

# Update database
sadrive update_sas

# Test upload
echo "Hello SA Drive!" > test.txt
sadrive upload test.txt

# Test navigate
sadrive navigate
```

## 🔧 Troubleshooting

### Lỗi "FileNotFoundError: accounts\\0.json"
- Đây là bug trong SA Drive với đường dẫn trên macOS
- Workaround: Sử dụng script fix_path_bug.py

### Lỗi "rateLimitExceeded"
- Google có giới hạn API calls
- Nghỉ 1-2 phút rồi thử lại
- Sử dụng `--transfers 1` để giảm tải

### Lỗi "quotaExceeded"
- Service account đã hết quota 750GB/day
- Gclone sẽ tự động chuyển sang SA khác

## 📊 Storage Capacity

| Setup | Projects | SAs | Storage |
|-------|----------|-----|---------|
| Test | 2 | 20 | 300GB |
| Small | 5 | 500 | 7.3TiB |
| Full | 12 | 1200 | 17TiB |

## 🎯 Commands Reference

```bash
# Xem storage details
sadrive details

# Upload file/folder
sadrive upload <path> [destination_id]

# Download
sadrive download <folder_id> <destination>

# Navigate interactively
sadrive navigate

# Mount as filesystem
sadrive mount

# Search files
sadrive search <term>

# Create folder
sadrive newfolder <name> [parent_id]

# Share file/folder
sadrive share <file_id>
```

## ⚠️ Lưu ý quan trọng

1. **Backup Service Account files**: Lưu trữ an toàn các file JSON
2. **Không share Service Accounts**: Giữ bí mật các credentials
3. **Tuân thủ Google ToS**: Sử dụng hợp lý, không abuse
4. **Monitor quota**: Theo dõi usage để tránh bị limit

## 🆘 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra SETUP_GUIDE.md
2. Chạy `sadrive --help`
3. Xem logs trong terminal
4. Thử restart terminal và chạy lại

---

**Chúc bạn thành công với 17TiB storage! 🎉**
